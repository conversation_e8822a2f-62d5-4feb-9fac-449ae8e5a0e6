#!/bin/bash
# STM32 Ninja - 通用函数库

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly WHITE='\033[1;37m'
readonly NC='\033[0m'

# 输出函数
info() { echo -e "${BLUE}[信息]${NC} $1"; }
success() { echo -e "${GREEN}[成功]${NC} $1"; }
warn() { echo -e "${YELLOW}[警告]${NC} $1"; }
error() { echo -e "${RED}[错误]${NC} $1"; }
step() { echo -e "${CYAN}➤${NC} $1"; }

# 加载配置
load_config() {
    local config_file="${NINJA_ROOT:-$(dirname "$(dirname "${BASH_SOURCE[0]}")")}/config/ninja.conf"
    if [ -f "$config_file" ]; then
        source "$config_file"
    else
        # 默认配置
        CUBECLT_PATH="/d/app/cubeclt/STM32CubeCLT_1.18.0"
        MSYS2_PATH="/d/app/msys2"
        DEFAULT_MCU="STM32F103C8T6"
        DEFAULT_BUILD_TYPE="Debug"
        DEFAULT_PROGRAMMER="stlink"
    fi
}

# 设置工具链
setup_toolchain() {
    load_config
    
    # 检查多种可能的路径结构
    local possible_paths=(
        "${CUBECLT_PATH}/GNU-tools-for-STM32/bin"
        "${CUBECLT_PATH}/bin"
        "${CUBECLT_PATH}"
    )
    
    local gcc_path=""
    for path in "${possible_paths[@]}"; do
        if [ -x "$path/arm-none-eabi-gcc.exe" ] || [ -x "$path/arm-none-eabi-gcc" ]; then
            gcc_path="$path"
            break
        fi
    done
    
    # 如果还是没找到，运行自动配置
    if [ -z "$gcc_path" ]; then
        warn "未找到GCC工具链，尝试自动配置..."
        "${SCRIPT_DIR}/setup_auto.sh"
        
        # 重新加载配置
        load_config
        
        # 再次尝试
        for path in "${possible_paths[@]}"; do
            if [ -x "$path/arm-none-eabi-gcc.exe" ] || [ -x "$path/arm-none-eabi-gcc" ]; then
                gcc_path="$path"
                break
            fi
        done
        
        if [ -z "$gcc_path" ]; then
            error "未找到GCC工具链，请检查STM32CubeCLT安装"
            return 1
        fi
    fi
    
    # 添加到PATH
    export PATH="$gcc_path:$PATH"
    
    # 验证
    if ! arm-none-eabi-gcc --version &>/dev/null; then
        error "GCC工具链初始化失败"
        return 1
    fi
    
    return 0
}

# 查找OpenOCD
find_openocd() {
    load_config
    
    local openocd_paths=(
        "${MSYS2_PATH}/mingw64/bin/openocd"
        "${CUBECLT_PATH}/OpenOCD/bin/openocd.exe"
        "/usr/bin/openocd"
        "openocd"
    )
    
    for path in "${openocd_paths[@]}"; do
        if command -v "$path" &>/dev/null; then
            echo "$path"
            return 0
        fi
    done
    
    return 1
}

# 查找Ninja
find_ninja() {
    load_config
    
    local ninja_paths=(
        "${CUBECLT_PATH}/Ninja/bin/ninja"
        "${MSYS2_PATH}/mingw64/bin/ninja"
        "/usr/bin/ninja"
        "ninja"
    )
    
    for path in "${ninja_paths[@]}"; do
        if command -v "$path" &>/dev/null; then
            echo "$path"
            return 0
        fi
    done
    
    return 1
}

# Ninja是唯一的构建工具，已移除make支持

# 格式化时间显示（毫秒级）
format_time() {
    local total_ms=$1
    local minutes=$((total_ms / 60000))
    local seconds=$(((total_ms % 60000) / 1000))
    local ms=$((total_ms % 1000))
    
    if [ $minutes -gt 0 ]; then
        printf "%d分%d.%03d秒" $minutes $seconds $ms
    elif [ $seconds -gt 0 ]; then
        printf "%d.%03d秒" $seconds $ms
    else
        printf "%dms" $ms
    fi
}

# 检测烧录器
detect_programmer() {
    local openocd=$(find_openocd)
    if [ -z "$openocd" ]; then
        return 1
    fi
    
    # 尝试连接STLink
    if $openocd -f interface/stlink.cfg -c "init; exit" &>/dev/null; then
        echo "stlink"
        return 0
    fi
    
    # 尝试连接CMSIS-DAP (SWD模式)
    if $openocd -f interface/cmsis-dap.cfg -c "transport select swd" -c "adapter speed 1000" -c "init; exit" &>/dev/null 2>&1; then
        echo "cmsis-dap"
        return 0
    fi
    
    # 尝试J-Link
    if $openocd -f interface/jlink.cfg -c "init; exit" &>/dev/null; then
        echo "jlink"
        return 0
    fi
    
    return 1
}

# 转换Windows路径
convert_path() {
    local path="$1"
    # 转换Git Bash路径格式
    if [[ "$path" =~ ^/[a-zA-Z]/ ]]; then
        # /d/path -> d:/path
        echo "${path:1:1}:${path:2}"
    else
        echo "$path"
    fi
}

# 加载配置（脚本启动时自动执行）
load_config