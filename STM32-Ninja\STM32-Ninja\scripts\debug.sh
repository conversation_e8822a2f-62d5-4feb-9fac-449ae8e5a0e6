#!/bin/bash
# STM32 Ninja - 调试脚本

# 加载通用函数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh" || exit 1

# 默认参数
PROJECT_DIR=""
GDB_PORT=3333
TELNET_PORT=4444

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --project)
            PROJECT_DIR="$2"
            shift 2
            ;;
        --gdb-port)
            GDB_PORT="$2"
            shift 2
            ;;
        *)
            error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 设置项目目录
if [ -z "$PROJECT_DIR" ]; then
    PROJECT_DIR="$(pwd)"
fi

cd "$PROJECT_DIR" || exit 1

# 查找ELF文件
find_elf_file() {
    for file in build/*.elf; do
        if [ -f "$file" ]; then
            echo "$file"
            return 0
        fi
    done
    return 1
}

# 主程序
main() {
    info "启动调试服务器..."
    
    # 查找ELF文件
    local elf_file=$(find_elf_file)
    if [ -z "$elf_file" ]; then
        error "未找到ELF文件"
        error "请先编译项目"
        exit 1
    fi
    
    info "调试文件: $elf_file"
    
    # 检测烧录器
    local programmer=$(detect_programmer)
    if [ -z "$programmer" ]; then
        error "未检测到烧录器"
        exit 1
    fi
    
    # 查找OpenOCD
    local openocd=$(find_openocd)
    if [ -z "$openocd" ]; then
        error "未找到OpenOCD工具"
        exit 1
    fi
    
    # 创建调试配置
    local debug_cfg="debug/debug.cfg"
    mkdir -p debug
    
    cat > "$debug_cfg" << EOF
# STM32 Ninja 调试配置
source [find interface/${programmer}.cfg]
transport select hla_swd
adapter speed 1000
source [find target/stm32f1x.cfg]

# GDB配置
gdb_port $GDB_PORT
gdb_memory_map enable
gdb_flash_program enable

# 初始化
init
halt
EOF
    
    echo ""
    success "调试服务器已启动"
    echo ""
    echo "连接方式："
    echo "  1. 在另一个终端运行GDB："
    echo "     arm-none-eabi-gdb $elf_file"
    echo ""
    echo "  2. 在GDB中连接："
    echo "     (gdb) target remote localhost:$GDB_PORT"
    echo "     (gdb) load"
    echo "     (gdb) monitor reset halt"
    echo "     (gdb) continue"
    echo ""
    echo "按 Ctrl+C 停止调试服务器"
    echo ""
    
    # 启动OpenOCD
    $openocd -f "$debug_cfg"
}

# 执行主程序
main