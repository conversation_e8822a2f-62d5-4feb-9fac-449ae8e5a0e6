#!/bin/bash
# STM32 Ninja - 从编译工程移除文件

# 加载通用函数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh" || exit 1

# 移除文件
remove_from_build() {
    local pattern="$1"
    
    if [ ! -f "build.ninja" ]; then
        error "未找到build.ninja文件"
        exit 1
    fi
    
    # 备份
    cp build.ninja build.ninja.bak
    
    # 查找匹配的行
    local matches=$(grep -n "^build .+: \(cc\|as\) .*$pattern" build.ninja | cut -d: -f1)
    
    if [ -z "$matches" ]; then
        warn "没有找到匹配的文件: $pattern"
        return 1
    fi
    
    # 显示将要删除的文件
    info "将要移除以下文件："
    for line_num in $matches; do
        local line=$(sed -n "${line_num}p" build.ninja)
        local file=$(echo "$line" | awk '{print $NF}')
        echo "  - $file"
    done
    
    # 确认
    echo -n "确认移除? (y/N): "
    read -r confirm
    if [[ "$confirm" != "y" && "$confirm" != "Y" ]]; then
        info "取消操作"
        rm -f build.ninja.bak
        return 0
    fi
    
    # 创建新文件，跳过匹配的行
    local temp_file=$(mktemp)
    local removed_objs=()
    
    while IFS= read -r line; do
        local skip=false
        for line_num in $matches; do
            if [ "$." = "$line_num" ]; then
                skip=true
                # 提取目标文件名
                local obj=$(echo "$line" | awk '{print $2}')
                removed_objs+=("$obj")
                break
            fi
        done
        
        if [ "$skip" = false ]; then
            # 如果是链接行，移除被删除的目标文件
            if [[ "$line" == build\ \$builddir/\$target.elf:\ link\ * ]]; then
                local new_line="$line"
                for obj in "${removed_objs[@]}"; do
                    new_line=$(echo "$new_line" | sed "s| $obj||g")
                done
                echo "$new_line" >> "$temp_file"
            else
                echo "$line" >> "$temp_file"
            fi
        fi
    done < build.ninja
    
    # 替换文件
    mv "$temp_file" build.ninja
    success "成功移除 ${#removed_objs[@]} 个文件"
}

# 主函数
main() {
    if [ $# -eq 0 ]; then
        error "用法: $0 <文件模式>"
        echo "示例: $0 test.c"
        echo "      $0 \"lib/*.c\""
        exit 1
    fi
    
    remove_from_build "$1"
}

main "$@"