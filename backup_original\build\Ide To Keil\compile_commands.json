[{"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Core\\Src\\gpio.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\__\\Core\\Src\\gpio.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\__\\Core\\Src\\gpio.d\" .\\..\\Core\\Src\\gpio.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Core\\Src\\i2c.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\__\\Core\\Src\\i2c.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\__\\Core\\Src\\i2c.d\" .\\..\\Core\\Src\\i2c.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Core\\Src\\stm32f1xx_hal_msp.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\__\\Core\\Src\\stm32f1xx_hal_msp.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\__\\Core\\Src\\stm32f1xx_hal_msp.d\" .\\..\\Core\\Src\\stm32f1xx_hal_msp.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Core\\Src\\stm32f1xx_it.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\__\\Core\\Src\\stm32f1xx_it.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\__\\Core\\Src\\stm32f1xx_it.d\" .\\..\\Core\\Src\\stm32f1xx_it.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Core\\Src\\system_stm32f1xx.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\__\\Core\\Src\\system_stm32f1xx.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\__\\Core\\Src\\system_stm32f1xx.d\" .\\..\\Core\\Src\\system_stm32f1xx.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Core\\Src\\tim.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\__\\Core\\Src\\tim.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\__\\Core\\Src\\tim.d\" .\\..\\Core\\Src\\tim.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal.d\" .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_cortex.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_cortex.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_cortex.d\" .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_cortex.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_dma.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_dma.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_dma.d\" .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_dma.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_exti.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_exti.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_exti.d\" .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_exti.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash.d\" .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash_ex.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash_ex.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash_ex.d\" .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash_ex.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio.d\" .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio_ex.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio_ex.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio_ex.d\" .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio_ex.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_i2c.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_i2c.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_i2c.d\" .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_i2c.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_pwr.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_pwr.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_pwr.d\" .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_pwr.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc.d\" .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc_ex.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc_ex.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc_ex.d\" .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc_ex.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim.d\" .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim_ex.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim_ex.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\__\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim_ex.d\" .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim_ex.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM\\Task\\Mpu6050Dmp\\MPU6050.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\Task\\Mpu6050Dmp\\MPU6050.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\Task\\Mpu6050Dmp\\MPU6050.d\" .\\Task\\Mpu6050Dmp\\MPU6050.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM\\Task\\Mpu6050Dmp\\inv_mpu.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\Task\\Mpu6050Dmp\\inv_mpu.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\Task\\Mpu6050Dmp\\inv_mpu.d\" .\\Task\\Mpu6050Dmp\\inv_mpu.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM\\Task\\Mpu6050Dmp\\inv_mpu_dmp_motion_driver.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\Task\\Mpu6050Dmp\\inv_mpu_dmp_motion_driver.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\Task\\Mpu6050Dmp\\inv_mpu_dmp_motion_driver.d\" .\\Task\\Mpu6050Dmp\\inv_mpu_dmp_motion_driver.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM\\Task\\Src\\Direction.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\Task\\Src\\Direction.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\Task\\Src\\Direction.d\" .\\Task\\Src\\Direction.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM\\Task\\Src\\Motor.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\Task\\Src\\Motor.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\Task\\Src\\Motor.d\" .\\Task\\Src\\Motor.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM\\Task\\Src\\OLED.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\Task\\Src\\OLED.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\Task\\Src\\OLED.d\" .\\Task\\Src\\OLED.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM\\Task\\Src\\fine_line.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\Task\\Src\\fine_line.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\Task\\Src\\fine_line.d\" .\\Task\\Src\\fine_line.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM\\Task\\Src\\key.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\Task\\Src\\key.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\Task\\Src\\key.d\" .\\Task\\Src\\key.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM\\Task\\Src\\main.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\Task\\Src\\main.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\Task\\Src\\main.d\" .\\Task\\Src\\main.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM\\Task\\Src\\pid.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\Task\\Src\\pid.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\Task\\Src\\pid.d\" .\\Task\\Src\\pid.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM\\Task\\Src\\task.c", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" -D\"USE_HAL_DRIVER\" -D\"STM32F103xB\" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\Ide To Keil\\.obj\\Task\\Src\\task.o\" --no_depend_system_headers --depend \".\\build\\Ide To Keil\\.obj\\Task\\Src\\task.d\" .\\Task\\Src\\task.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "file": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM\\startup_stm32f103xb.s", "command": "\"c:\\Keil_v5\\ARM\\ARMCC\\bin\\armasm.exe\" --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I\"RTE/_Ide To Keil\" --cpu Cortex-M3 --li -g -o \".\\build\\Ide To Keil\\.obj\\startup_stm32f103xb.o\" --depend \".\\build\\Ide To Keil\\.obj\\startup_stm32f103xb.d\" .\\startup_stm32f103xb.s"}]