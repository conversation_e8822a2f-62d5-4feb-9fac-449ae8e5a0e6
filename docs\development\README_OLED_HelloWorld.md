# STM32F103C8 OLED Hello World 项目

## 项目概述
这是一个简化的STM32F103C8项目，专门用于在OLED显示屏上显示"Hello World"。项目已从复杂的多功能系统简化为单一的OLED显示功能。

## 硬件配置
- 微控制器: STM32F103C8T6
- 显示屏: SSD1306 OLED (128x64)
- 通信接口: I2C (软件模拟)
- 引脚配置:
  - PB12: SCL (时钟线)
  - PB13: SDA (数据线)

## 项目结构
```
Task/
├── Src/
│   ├── main.c          # 主程序文件 (简化版)
│   └── OLED.c          # OLED驱动程序 (软件I2C)
└── Inc/
    ├── OLED.h          # OLED驱动头文件
    └── OLED_Font.h     # OLED字体库

Core/
├── Src/
│   ├── main.c          # STM32CubeMX生成的主文件
│   ├── gpio.c          # GPIO配置
│   ├── stm32f1xx_hal_msp.c  # HAL MSP配置
│   ├── stm32f1xx_it.c      # 中断处理 (简化版)
│   └── system_stm32f1xx.c  # 系统配置
└── Inc/
    ├── main.h          # 主程序头文件
    ├── gpio.h          # GPIO头文件
    ├── stm32f1xx_hal_conf.h  # HAL配置
    └── stm32f1xx_it.h       # 中断头文件
```

## 功能特性
- ✅ 简洁的代码结构
- ✅ 软件I2C实现 (无需HAL I2C库)
- ✅ 基本的OLED显示功能
- ✅ 显示"Hello World"文本
- ✅ 移除了所有不必要的模块 (MPU6050, 电机控制, PID, 按键等)

## 编译状态
✅ **编译成功** - 项目已成功编译并生成hex文件

### 编译信息
- Flash使用: 7020 字节 / 512 KB (1.34%)
- RAM使用: 1040 字节 / 128 KB (0.79%)
- 生成文件: `build/Ide To Keil.hex`

## 编译和烧录
1. 使用ninja构建系统编译项目:
   ```bash
   ninja
   ```
2. 生成的hex文件位于 `build/Ide To Keil.hex`
3. 使用ST-Link或其他烧录工具将hex文件烧录到STM32F103C8

## 主要代码
### main.c (简化版)
```c
int main(void) {
    HAL_Init();
    SystemClock_Config();
    MX_GPIO_Init();
    
    OLED_Init();
    OLED_Clear();
    OLED_ShowString(1, 1, "Hello World");
    
    while(1) {
        HAL_Delay(1000);
    }
}
```

## 开发环境
- 编译器: ARM GCC
- 构建系统: Ninja
- HAL库: STM32F1xx HAL Driver (简化版)
- 开发工具: STM32CubeMX + 自定义构建脚本

## 项目简化历史
- 原项目包含: MPU6050陀螺仪、电机控制、PID算法、循线检测、按键输入、任务调度等
- 简化后保留: 仅OLED显示功能
- 移除的文件: 11个源文件和对应头文件，整个MPU6050DMP目录
- 代码行数减少: 从数千行减少到不到100行核心代码

## 故障排除
如果遇到编译问题，请检查:
1. ARM GCC工具链是否正确安装
2. Ninja构建系统是否可用
3. 所有必要的头文件路径是否正确配置
4. HAL库配置是否正确 (特别是I2C模块应该被禁用)
