Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C2_Init) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C2_Init) refers to i2c.o(.bss) for .bss
    stm32f1xx_it.o(i.SysTick_Handler) refers to stm32f1xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f1xx_it.o(i.TIM2_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f1xx_it.o(i.TIM2_IRQHandler) refers to tim.o(.bss) for htim2
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.data) for .data
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.constdata) for .constdata
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM2_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM2_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM2_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM2_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM2_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM4_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM4_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM4_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM4_Init) refers to tim.o(.bss) for .bss
    stm32f1xx_hal.o(i.HAL_DeInit) refers to stm32f1xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_IncTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_InitTick) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to llushr.o(.text) for __aeabi_llsr
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f1xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to stm32f1xx_hal_rcc.o(.constdata) for .constdata
    stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc_ex.o(.constdata) for .constdata
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAError) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    MPU6050.o(i.MPU6050_DMP_Get_Data) refers to inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) for dmp_read_fifo
    MPU6050.o(i.MPU6050_DMP_Get_Data) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    MPU6050.o(i.MPU6050_DMP_Get_Data) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    MPU6050.o(i.MPU6050_DMP_Get_Data) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    MPU6050.o(i.MPU6050_DMP_Get_Data) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    MPU6050.o(i.MPU6050_DMP_Get_Data) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    MPU6050.o(i.MPU6050_DMP_Get_Data) refers to asin.o(i.asin) for asin
    MPU6050.o(i.MPU6050_DMP_Get_Data) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    MPU6050.o(i.MPU6050_DMP_Get_Data) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    MPU6050.o(i.MPU6050_DMP_Get_Data) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    MPU6050.o(i.MPU6050_DMP_Get_Data) refers to atan2.o(i.atan2) for atan2
    MPU6050.o(i.MPU6050_DMP_Init) refers to inv_mpu.o(i.mpu_init) for mpu_init
    MPU6050.o(i.MPU6050_DMP_Init) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    MPU6050.o(i.MPU6050_DMP_Init) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    MPU6050.o(i.MPU6050_DMP_Init) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    MPU6050.o(i.MPU6050_DMP_Init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware) for dmp_load_motion_driver_firmware
    MPU6050.o(i.MPU6050_DMP_Init) refers to MPU6050.o(i.inv_row_2_scale) for inv_row_2_scale
    MPU6050.o(i.MPU6050_DMP_Init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation) for dmp_set_orientation
    MPU6050.o(i.MPU6050_DMP_Init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) for dmp_enable_feature
    MPU6050.o(i.MPU6050_DMP_Init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate) for dmp_set_fifo_rate
    MPU6050.o(i.MPU6050_DMP_Init) refers to MPU6050.o(i.run_self_test) for run_self_test
    MPU6050.o(i.MPU6050_DMP_Init) refers to inv_mpu.o(i.mpu_set_dmp_state) for mpu_set_dmp_state
    MPU6050.o(i.MPU6050_DMP_Init) refers to MPU6050.o(.data) for .data
    MPU6050.o(i.run_self_test) refers to inv_mpu.o(i.mpu_run_self_test) for mpu_run_self_test
    MPU6050.o(i.run_self_test) refers to inv_mpu.o(i.mpu_get_gyro_sens) for mpu_get_gyro_sens
    MPU6050.o(i.run_self_test) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    MPU6050.o(i.run_self_test) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    MPU6050.o(i.run_self_test) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    MPU6050.o(i.run_self_test) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) for dmp_set_gyro_bias
    MPU6050.o(i.run_self_test) refers to inv_mpu.o(i.mpu_get_accel_sens) for mpu_get_accel_sens
    MPU6050.o(i.run_self_test) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) for dmp_set_accel_bias
    inv_mpu.o(i.accel_self_test) refers to inv_mpu.o(i.get_accel_prod_shift) for get_accel_prod_shift
    inv_mpu.o(i.accel_self_test) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    inv_mpu.o(i.accel_self_test) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    inv_mpu.o(i.accel_self_test) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    inv_mpu.o(i.accel_self_test) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    inv_mpu.o(i.get_accel_prod_shift) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    inv_mpu.o(i.get_accel_prod_shift) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(i.get_accel_prod_shift) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.get_accel_prod_shift) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.get_st_biases) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    inv_mpu.o(i.get_st_biases) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.get_st_biases) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    inv_mpu.o(i.get_st_biases) refers to llsdiv.o(.text) for __aeabi_ldivmod
    inv_mpu.o(i.get_st_biases) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.get_st_biases) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.gyro_self_test) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    inv_mpu.o(i.gyro_self_test) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    inv_mpu.o(i.gyro_self_test) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    inv_mpu.o(i.gyro_self_test) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(i.gyro_self_test) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    inv_mpu.o(i.gyro_self_test) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    inv_mpu.o(i.gyro_self_test) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.gyro_self_test) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_configure_fifo) refers to inv_mpu.o(i.set_int_enable) for set_int_enable
    inv_mpu.o(i.mpu_configure_fifo) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_configure_fifo) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_accel_fsr) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_accel_reg) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    inv_mpu.o(i.mpu_get_accel_reg) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    inv_mpu.o(i.mpu_get_accel_reg) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_accel_reg) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_get_accel_sens) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_dmp_state) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_fifo_config) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_gyro_fsr) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_gyro_reg) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    inv_mpu.o(i.mpu_get_gyro_reg) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    inv_mpu.o(i.mpu_get_gyro_reg) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_gyro_reg) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_get_gyro_sens) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_int_status) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    inv_mpu.o(i.mpu_get_int_status) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_int_status) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_get_lpf) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_power_state) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_sample_rate) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_temperature) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    inv_mpu.o(i.mpu_get_temperature) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    inv_mpu.o(i.mpu_get_temperature) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    inv_mpu.o(i.mpu_get_temperature) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    inv_mpu.o(i.mpu_get_temperature) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    inv_mpu.o(i.mpu_get_temperature) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    inv_mpu.o(i.mpu_get_temperature) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    inv_mpu.o(i.mpu_get_temperature) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    inv_mpu.o(i.mpu_get_temperature) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    inv_mpu.o(i.mpu_get_temperature) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_temperature) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_init) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    inv_mpu.o(i.mpu_init) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.mpu_init) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_init) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_load_firmware) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu.o(i.mpu_load_firmware) refers to inv_mpu.o(i.mpu_read_mem) for mpu_read_mem
    inv_mpu.o(i.mpu_load_firmware) refers to memcmp.o(.text) for memcmp
    inv_mpu.o(i.mpu_load_firmware) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    inv_mpu.o(i.mpu_load_firmware) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_load_firmware) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(i.mpu_set_int_latched) for mpu_set_int_latched
    inv_mpu.o(i.mpu_lp_accel_mode) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_lp_accel_mode) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_gyro_fsr) for mpu_get_gyro_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_lpf) for mpu_get_lpf
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_sample_rate) for mpu_get_sample_rate
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.set_int_enable) for set_int_enable
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_lp_accel_mode) for mpu_lp_accel_mode
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_read_fifo) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    inv_mpu.o(i.mpu_read_fifo) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_read_fifo) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    inv_mpu.o(i.mpu_read_fifo) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_read_fifo) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_read_fifo_stream) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    inv_mpu.o(i.mpu_read_fifo_stream) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_read_fifo_stream) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_read_fifo_stream) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_read_mem) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    inv_mpu.o(i.mpu_read_mem) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    inv_mpu.o(i.mpu_read_mem) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_read_mem) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_read_reg) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    inv_mpu.o(i.mpu_read_reg) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_read_reg) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_reg_dump) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    inv_mpu.o(i.mpu_reg_dump) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_reg_dump) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_reset_fifo) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    inv_mpu.o(i.mpu_reset_fifo) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.mpu_reset_fifo) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_reset_fifo) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_gyro_fsr) for mpu_get_gyro_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_lpf) for mpu_get_lpf
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_sample_rate) for mpu_get_sample_rate
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.get_st_biases) for get_st_biases
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.accel_self_test) for accel_self_test
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.gyro_self_test) for gyro_self_test
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_accel_bias) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    inv_mpu.o(i.mpu_set_accel_bias) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    inv_mpu.o(i.mpu_set_accel_bias) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_accel_bias) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_set_accel_fsr) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    inv_mpu.o(i.mpu_set_accel_fsr) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_accel_fsr) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_set_bypass) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    inv_mpu.o(i.mpu_set_bypass) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    inv_mpu.o(i.mpu_set_bypass) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.mpu_set_bypass) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_bypass) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.set_int_enable) for set_int_enable
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_set_dmp_state) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_dmp_state) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_set_gyro_fsr) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    inv_mpu.o(i.mpu_set_gyro_fsr) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_gyro_fsr) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_set_int_latched) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    inv_mpu.o(i.mpu_set_int_latched) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_int_latched) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_set_int_level) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_lpf) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    inv_mpu.o(i.mpu_set_lpf) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_lpf) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_set_sample_rate) refers to inv_mpu.o(i.mpu_lp_accel_mode) for mpu_lp_accel_mode
    inv_mpu.o(i.mpu_set_sample_rate) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    inv_mpu.o(i.mpu_set_sample_rate) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_set_sample_rate) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_sample_rate) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_set_sensors) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    inv_mpu.o(i.mpu_set_sensors) refers to inv_mpu.o(i.mpu_set_int_latched) for mpu_set_int_latched
    inv_mpu.o(i.mpu_set_sensors) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.mpu_set_sensors) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_sensors) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.mpu_write_mem) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    inv_mpu.o(i.mpu_write_mem) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_write_mem) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(i.set_int_enable) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    inv_mpu.o(i.set_int_enable) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.set_int_enable) refers to i2c.o(.bss) for hi2c2
    inv_mpu.o(.data) refers to inv_mpu.o(.constdata) for reg
    inv_mpu.o(.data) refers to inv_mpu.o(.constdata) for hw
    inv_mpu.o(.data) refers to inv_mpu.o(.constdata) for test
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) refers to aeabi_memset.o(.text) for __aeabi_memset
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal) for dmp_enable_gyro_cal
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) for dmp_set_tap_thresh
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes) for dmp_set_tap_axes
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count) for dmp_set_tap_count
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time) for dmp_set_tap_time
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi) for dmp_set_tap_time_multi
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh) for dmp_set_shake_reject_thresh
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time) for dmp_set_shake_reject_time
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout) for dmp_set_shake_reject_timeout
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) for dmp_enable_lp_quat
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) for dmp_enable_6x_lp_quat
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) refers to aeabi_memset.o(.text) for __aeabi_memset
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_get_enabled_features) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_get_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_step_count) refers to inv_mpu.o(i.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_walk_time) refers to inv_mpu.o(i.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware) refers to inv_mpu.o(i.mpu_load_firmware) for mpu_load_firmware
    inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware) refers to inv_mpu_dmp_motion_driver.o(.constdata) for .constdata
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu.o(i.mpu_read_fifo_stream) for mpu_read_fifo_stream
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_register_android_orient_cb) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_register_tap_cb) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) refers to inv_mpu.o(i.mpu_get_accel_sens) for mpu_get_accel_sens
    inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_set_interrupt_mode) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_step_count) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_walk_time) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to inv_mpu.o(i.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    Direction.o(i.ControlYawMovement) refers to MPU6050.o(i.MPU6050_DMP_Get_Data) for MPU6050_DMP_Get_Data
    Direction.o(i.ControlYawMovement) refers to pid.o(i.PID_realize) for PID_realize
    Direction.o(i.ControlYawMovement) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    Direction.o(i.ControlYawMovement) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    Direction.o(i.ControlYawMovement) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    Direction.o(i.ControlYawMovement) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    Direction.o(i.ControlYawMovement) refers to Motor.o(i.SetSpeedR) for SetSpeedR
    Direction.o(i.ControlYawMovement) refers to Motor.o(i.SetSpeedL) for SetSpeedL
    Direction.o(i.ControlYawMovement) refers to pid.o(.bss) for pidMPU6050YawMovement
    Direction.o(i.ControlYawMovement) refers to main.o(.data) for g_fMPU6050YawMovePidOut1
    Direction.o(i.ControlYawMovement) refers to main.o(.data) for g_fMPU6050YawMovePidOut2
    Motor.o(i.SetSpeedL) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    Motor.o(i.SetSpeedL) refers to Motor.o(i.abds) for abds
    Motor.o(i.SetSpeedL) refers to tim.o(.bss) for htim2
    Motor.o(i.SetSpeedR) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    Motor.o(i.SetSpeedR) refers to Motor.o(i.abds) for abds
    Motor.o(i.SetSpeedR) refers to tim.o(.bss) for htim2
    OLED.o(i.OLED_Clear) refers to OLED.o(i.OLED_SetCursor) for OLED_SetCursor
    OLED.o(i.OLED_Clear) refers to OLED.o(i.OLED_WriteData) for OLED_WriteData
    OLED.o(i.OLED_I2C_Init) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    OLED.o(i.OLED_I2C_Init) refers to OLED.o(i.OLED_W_SCL) for OLED_W_SCL
    OLED.o(i.OLED_I2C_Init) refers to OLED.o(i.OLED_W_SDA) for OLED_W_SDA
    OLED.o(i.OLED_I2C_SendByte) refers to OLED.o(i.OLED_W_SDA) for OLED_W_SDA
    OLED.o(i.OLED_I2C_SendByte) refers to OLED.o(i.OLED_W_SCL) for OLED_W_SCL
    OLED.o(i.OLED_I2C_Start) refers to OLED.o(i.OLED_W_SDA) for OLED_W_SDA
    OLED.o(i.OLED_I2C_Start) refers to OLED.o(i.OLED_W_SCL) for OLED_W_SCL
    OLED.o(i.OLED_I2C_Stop) refers to OLED.o(i.OLED_W_SDA) for OLED_W_SDA
    OLED.o(i.OLED_I2C_Stop) refers to OLED.o(i.OLED_W_SCL) for OLED_W_SCL
    OLED.o(i.OLED_Init) refers to OLED.o(i.OLED_I2C_Init) for OLED_I2C_Init
    OLED.o(i.OLED_Init) refers to OLED.o(i.OLED_WriteCommand) for OLED_WriteCommand
    OLED.o(i.OLED_Init) refers to OLED.o(i.OLED_Clear) for OLED_Clear
    OLED.o(i.OLED_SetCursor) refers to OLED.o(i.OLED_WriteCommand) for OLED_WriteCommand
    OLED.o(i.OLED_ShowBinNum) refers to OLED.o(i.OLED_Pow) for OLED_Pow
    OLED.o(i.OLED_ShowBinNum) refers to OLED.o(i.OLED_ShowChar) for OLED_ShowChar
    OLED.o(i.OLED_ShowChar) refers to OLED.o(i.OLED_SetCursor) for OLED_SetCursor
    OLED.o(i.OLED_ShowChar) refers to OLED.o(i.OLED_WriteData) for OLED_WriteData
    OLED.o(i.OLED_ShowChar) refers to OLED.o(.constdata) for .constdata
    OLED.o(i.OLED_ShowHexNum) refers to OLED.o(i.OLED_Pow) for OLED_Pow
    OLED.o(i.OLED_ShowHexNum) refers to OLED.o(i.OLED_ShowChar) for OLED_ShowChar
    OLED.o(i.OLED_ShowNum) refers to OLED.o(i.OLED_Pow) for OLED_Pow
    OLED.o(i.OLED_ShowNum) refers to OLED.o(i.OLED_ShowChar) for OLED_ShowChar
    OLED.o(i.OLED_ShowSignedNum) refers to OLED.o(i.OLED_ShowChar) for OLED_ShowChar
    OLED.o(i.OLED_ShowSignedNum) refers to OLED.o(i.OLED_Pow) for OLED_Pow
    OLED.o(i.OLED_ShowString) refers to OLED.o(i.OLED_ShowChar) for OLED_ShowChar
    OLED.o(i.OLED_W_SCL) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    OLED.o(i.OLED_W_SDA) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    OLED.o(i.OLED_WriteCommand) refers to OLED.o(i.OLED_I2C_Start) for OLED_I2C_Start
    OLED.o(i.OLED_WriteCommand) refers to OLED.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    OLED.o(i.OLED_WriteCommand) refers to OLED.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    OLED.o(i.OLED_WriteData) refers to OLED.o(i.OLED_I2C_Start) for OLED_I2C_Start
    OLED.o(i.OLED_WriteData) refers to OLED.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    OLED.o(i.OLED_WriteData) refers to OLED.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    fine_line.o(i.Find_Line_Begins) refers to fine_line.o(i.line_detect) for line_detect
    fine_line.o(i.Find_Line_Begins) refers to fine_line.o(i.get_LedFind_Scan) for get_LedFind_Scan
    fine_line.o(i.Find_Line_Begins) refers to OLED.o(i.OLED_ShowString) for OLED_ShowString
    fine_line.o(i.Find_Line_Begins) refers to Motor.o(i.SetSpeedR) for SetSpeedR
    fine_line.o(i.Find_Line_Begins) refers to Motor.o(i.SetSpeedL) for SetSpeedL
    fine_line.o(i.Find_Line_Begins) refers to fine_line.o(.data) for .data
    fine_line.o(i.Find_Line_Begins) refers to fine_line.o(.bss) for .bss
    fine_line.o(i.get_LedFind_Scan) refers to fine_line.o(.data) for .data
    fine_line.o(i.line_detect) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    fine_line.o(i.line_detect) refers to fine_line.o(.data) for .data
    key.o(i.KEY_GetTaskIndex) refers to key.o(.data) for .data
    key.o(i.KEY_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    key.o(i.KEY_Init) refers to key.o(i.KEY_UpdateOLED) for KEY_UpdateOLED
    key.o(i.KEY_IsSelectionMode) refers to key.o(.data) for .data
    key.o(i.KEY_IsTaskExecuted) refers to key.o(.data) for .data
    key.o(i.KEY_Scan) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key.o(i.KEY_Scan) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    key.o(i.KEY_Scan) refers to key.o(.data) for .data
    key.o(i.KEY_SetTaskExecuted) refers to key.o(i.KEY_UpdateOLED) for KEY_UpdateOLED
    key.o(i.KEY_SetTaskExecuted) refers to key.o(.data) for .data
    key.o(i.KEY_SwitchTask) refers to key.o(i.KEY_UpdateOLED) for KEY_UpdateOLED
    key.o(i.KEY_SwitchTask) refers to key.o(.data) for .data
    key.o(i.KEY_UpdateOLED) refers to OLED.o(i.OLED_Clear) for OLED_Clear
    key.o(i.KEY_UpdateOLED) refers to OLED.o(i.OLED_ShowString) for OLED_ShowString
    key.o(i.KEY_UpdateOLED) refers to OLED.o(i.OLED_ShowNum) for OLED_ShowNum
    key.o(i.KEY_UpdateOLED) refers to key.o(.data) for .data
    key.o(i.KEY_UpdateSelectionTimer) refers to key.o(i.KEY_UpdateOLED) for KEY_UpdateOLED
    key.o(i.KEY_UpdateSelectionTimer) refers to key.o(.data) for .data
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C2_Init) for MX_I2C2_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to tim.o(i.MX_TIM4_Init) for MX_TIM4_Init
    main.o(i.main) refers to OLED.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to pid.o(i.PID_init) for PID_init
    main.o(i.main) refers to key.o(i.KEY_Init) for KEY_Init
    main.o(i.main) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    main.o(i.main) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    main.o(i.main) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    main.o(i.main) refers to key.o(i.KEY_UpdateOLED) for KEY_UpdateOLED
    main.o(i.main) refers to key.o(i.KEY_IsSelectionMode) for KEY_IsSelectionMode
    main.o(i.main) refers to key.o(i.KEY_Scan) for KEY_Scan
    main.o(i.main) refers to key.o(i.KEY_SwitchTask) for KEY_SwitchTask
    main.o(i.main) refers to key.o(i.KEY_UpdateSelectionTimer) for KEY_UpdateSelectionTimer
    main.o(i.main) refers to key.o(i.KEY_IsTaskExecuted) for KEY_IsTaskExecuted
    main.o(i.main) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.main) refers to key.o(i.KEY_GetTaskIndex) for KEY_GetTaskIndex
    main.o(i.main) refers to task.o(i.task_0) for task_0
    main.o(i.main) refers to task.o(i.task_1) for task_1
    main.o(i.main) refers to task.o(i.task_2) for task_2
    main.o(i.main) refers to task.o(i.task_3) for task_3
    main.o(i.main) refers to task.o(i.task_4) for task_4
    main.o(i.main) refers to key.o(i.KEY_SetTaskExecuted) for KEY_SetTaskExecuted
    main.o(i.main) refers to tim.o(.bss) for htim2
    main.o(i.main) refers to task.o(.data) for Do_count
    pid.o(i.PID_init) refers to pid.o(.bss) for .bss
    pid.o(i.PID_realize) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    pid.o(i.PID_realize) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    pid.o(i.PID_realize) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pid.o(i.PI_realize) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    pid.o(i.PI_realize) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    pid.o(i.PI_realize) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pid.o(i.P_realize) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    pid.o(i.P_realize) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    task.o(i.task_0) refers to OLED.o(i.OLED_ShowSignedNum) for OLED_ShowSignedNum
    task.o(i.task_0) refers to Motor.o(i.SetSpeedR) for SetSpeedR
    task.o(i.task_0) refers to Motor.o(i.SetSpeedL) for SetSpeedL
    task.o(i.task_0) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    task.o(i.task_1) refers to OLED.o(i.OLED_ShowSignedNum) for OLED_ShowSignedNum
    task.o(i.task_1) refers to Motor.o(i.SetSpeedR) for SetSpeedR
    task.o(i.task_1) refers to Motor.o(i.SetSpeedL) for SetSpeedL
    task.o(i.task_1) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    task.o(i.task_1) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    task.o(i.task_1) refers to MPU6050.o(i.MPU6050_DMP_Init) for MPU6050_DMP_Init
    task.o(i.task_1) refers to Direction.o(i.ControlYawMovement) for ControlYawMovement
    task.o(i.task_1) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    task.o(i.task_1) refers to fine_line.o(i.line_detect) for line_detect
    task.o(i.task_1) refers to fine_line.o(i.get_LedFind_Scan) for get_LedFind_Scan
    task.o(i.task_1) refers to main.o(.data) for g_fMPU6050YawMovePidOut1
    task.o(i.task_1) refers to main.o(.data) for g_fMPU6050YawMovePidOut2
    task.o(i.task_1) refers to task.o(.data) for .data
    task.o(i.task_1) refers to fine_line.o(.data) for have_line
    task.o(i.task_2) refers to OLED.o(i.OLED_Clear) for OLED_Clear
    task.o(i.task_2) refers to OLED.o(i.OLED_ShowString) for OLED_ShowString
    task.o(i.task_2) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    task.o(i.task_2) refers to MPU6050.o(i.MPU6050_DMP_Init) for MPU6050_DMP_Init
    task.o(i.task_2) refers to Direction.o(i.ControlYawMovement) for ControlYawMovement
    task.o(i.task_2) refers to fine_line.o(i.line_detect) for line_detect
    task.o(i.task_2) refers to fine_line.o(i.get_LedFind_Scan) for get_LedFind_Scan
    task.o(i.task_2) refers to OLED.o(i.OLED_ShowNum) for OLED_ShowNum
    task.o(i.task_2) refers to Motor.o(i.SetSpeedR) for SetSpeedR
    task.o(i.task_2) refers to Motor.o(i.SetSpeedL) for SetSpeedL
    task.o(i.task_2) refers to MPU6050.o(i.MPU6050_DMP_Get_Data) for MPU6050_DMP_Get_Data
    task.o(i.task_2) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    task.o(i.task_2) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    task.o(i.task_2) refers to fine_line.o(i.Find_Line_Begins) for Find_Line_Begins
    task.o(i.task_2) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    task.o(i.task_2) refers to task.o(.data) for .data
    task.o(i.task_2) refers to fine_line.o(.data) for have_line
    task.o(i.task_3) refers to OLED.o(i.OLED_ShowString) for OLED_ShowString
    task.o(i.task_3) refers to MPU6050.o(i.MPU6050_DMP_Init) for MPU6050_DMP_Init
    task.o(i.task_3) refers to Direction.o(i.ControlYawMovement) for ControlYawMovement
    task.o(i.task_3) refers to fine_line.o(i.line_detect) for line_detect
    task.o(i.task_3) refers to fine_line.o(i.get_LedFind_Scan) for get_LedFind_Scan
    task.o(i.task_3) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    task.o(i.task_3) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    task.o(i.task_3) refers to fine_line.o(i.Find_Line_Begins) for Find_Line_Begins
    task.o(i.task_3) refers to Motor.o(i.SetSpeedR) for SetSpeedR
    task.o(i.task_3) refers to Motor.o(i.SetSpeedL) for SetSpeedL
    task.o(i.task_3) refers to fine_line.o(.data) for have_line
    task.o(i.task_3) refers to task.o(.data) for .data
    task.o(i.task_4) refers to OLED.o(i.OLED_Clear) for OLED_Clear
    task.o(i.task_4) refers to OLED.o(i.OLED_ShowString) for OLED_ShowString
    task.o(i.task_4) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    task.o(i.task_4) refers to OLED.o(i.OLED_ShowNum) for OLED_ShowNum
    task.o(i.task_4) refers to MPU6050.o(i.MPU6050_DMP_Init) for MPU6050_DMP_Init
    task.o(i.task_4) refers to Direction.o(i.ControlYawMovement) for ControlYawMovement
    task.o(i.task_4) refers to Motor.o(i.SetSpeedR) for SetSpeedR
    task.o(i.task_4) refers to Motor.o(i.SetSpeedL) for SetSpeedL
    task.o(i.task_4) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    task.o(i.task_4) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    task.o(i.task_4) refers to task.o(.data) for .data
    startup_stm32f103xb.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(.text) for Reset_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f103xb.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(.text) refers to system_stm32f1xx.o(i.SystemInit) for SystemInit
    startup_stm32f103xb.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f103xb.o(.text) refers to startup_stm32f103xb.o(HEAP) for Heap_Mem
    startup_stm32f103xb.o(.text) refers to startup_stm32f103xb.o(STACK) for Stack_Mem
    llsdiv.o(.text) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    aeabi_memset.o(.text) refers to rt_memclr.o(.text) for _memset
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffixr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixu) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixur) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    frleqf.o(x$fpl$frleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frleqf.o(x$fpl$frleqf) refers to fleqf.o(x$fpl$fleqf) for __fpl_fcmple_InfNaN
    scalbnf.o(x$fpl$scalbnf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbnf.o(x$fpl$scalbnf) refers to fcheck1.o(x$fpl$fcheck1) for __fpl_fcheck_NaN1
    asin.o(i.__softfp_asin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin.o(i.__softfp_asin) refers to asin.o(i.asin) for asin
    asin.o(i.asin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin.o(i.asin) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    asin.o(i.asin) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    asin.o(i.asin) refers to _rserrno.o(.text) for __set_errno
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    asin.o(i.asin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    asin.o(i.asin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin.o(i.asin) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    asin.o(i.asin) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    asin.o(i.asin) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    asin.o(i.asin) refers to sqrt.o(i.sqrt) for sqrt
    asin.o(i.asin) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    asin.o(i.asin) refers to asin.o(.constdata) for .constdata
    asin.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin_x.o(i.____softfp_asin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin_x.o(i.____softfp_asin$lsc) refers to asin_x.o(i.__asin$lsc) for __asin$lsc
    asin_x.o(i.__asin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin_x.o(i.__asin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin_x.o(i.__asin$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    asin_x.o(i.__asin$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    asin_x.o(i.__asin$lsc) refers to _rserrno.o(.text) for __set_errno
    asin_x.o(i.__asin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin_x.o(i.__asin$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    asin_x.o(i.__asin$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    asin_x.o(i.__asin$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    asin_x.o(i.__asin$lsc) refers to sqrt.o(i.sqrt) for sqrt
    asin_x.o(i.__asin$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    asin_x.o(i.__asin$lsc) refers to asin_x.o(.constdata) for .constdata
    asin_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.atan2) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2.o(i.atan2) refers to _rserrno.o(.text) for __set_errno
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.__atan2$lsc) for __atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.__atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.__atan2$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2_x.o(i.__atan2$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2_x.o(i.__atan2$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2_x.o(i.__atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcheck1.o(x$fpl$fcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcheck1.o(x$fpl$fcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    fleqf.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fleqf.o(x$fpl$fleqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.atan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.__atan$lsc) for __atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan_x.o(i.__atan$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan_x.o(i.__atan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan_x.o(i.__atan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan_x.o(i.__atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan_x.o(i.__atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dsqrt_noumaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_noumaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f103xb.o(.text) for __user_initial_stackheap
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (56 bytes).
    Removing stm32f1xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_it.o(.rrx_text), (6 bytes).
    Removing system_stm32f1xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f1xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f1xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f1xx.o(i.SystemCoreClockUpdate), (104 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (32 bytes).
    Removing tim.o(i.HAL_TIM_Encoder_MspDeInit), (40 bytes).
    Removing stm32f1xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DeInit), (32 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f1xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.DMA_SetConfig), (42 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Abort), (70 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT), (152 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit), (92 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler), (340 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Init), (92 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (532 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (74 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT), (112 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (82 bytes).
    Removing stm32f1xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (104 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (140 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (164 bytes).
    Removing stm32f1xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord), (28 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode), (92 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation), (84 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (264 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (4 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program), (128 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT), (100 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock), (40 bytes).
    Removing stm32f1xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (100 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (168 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetUserData), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (200 bytes).
    Removing stm32f1xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit), (280 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_ConfigEventout), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_DisableEventout), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_EnableEventout), (16 bytes).
    Removing stm32f1xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (68 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (196 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (560 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (58 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (364 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (94 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (592 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (340 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (196 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (552 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (320 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (452 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (212 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (300 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (340 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (184 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (452 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (220 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (400 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (208 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (372 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (228 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (124 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (352 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (116 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (352 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (116 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (348 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (228 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (124 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAAbort), (188 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAError), (54 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt), (266 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Flush_DR), (16 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_ITError), (344 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (210 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (232 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead), (236 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite), (156 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (182 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Master_ADDR), (344 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Master_SB), (140 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF), (168 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR), (70 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Slave_AF), (144 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF), (348 bytes).
    Removing stm32f1xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit), (220 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (144 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq), (32 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (44 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (164 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (236 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.constdata), (18 bytes).
    Removing stm32f1xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start), (80 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (152 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (292 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start), (184 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (416 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (228 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start), (156 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (404 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (200 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop), (112 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (196 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (176 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (104 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (124 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (100 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (120 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (404 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (200 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (112 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (196 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (176 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig), (88 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (84 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (128 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (184 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (140 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (152 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (352 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (192 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (152 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (352 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (192 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (94 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing inv_mpu.o(.rev16_text), (4 bytes).
    Removing inv_mpu.o(.revsh_text), (4 bytes).
    Removing inv_mpu.o(.rrx_text), (6 bytes).
    Removing inv_mpu.o(i.mpu_get_accel_reg), (100 bytes).
    Removing inv_mpu.o(i.mpu_get_compass_fsr), (6 bytes).
    Removing inv_mpu.o(i.mpu_get_compass_reg), (6 bytes).
    Removing inv_mpu.o(i.mpu_get_compass_sample_rate), (8 bytes).
    Removing inv_mpu.o(i.mpu_get_dmp_state), (16 bytes).
    Removing inv_mpu.o(i.mpu_get_fifo_config), (16 bytes).
    Removing inv_mpu.o(i.mpu_get_gyro_reg), (104 bytes).
    Removing inv_mpu.o(i.mpu_get_int_status), (76 bytes).
    Removing inv_mpu.o(i.mpu_get_power_state), (20 bytes).
    Removing inv_mpu.o(i.mpu_get_temperature), (148 bytes).
    Removing inv_mpu.o(i.mpu_lp_motion_interrupt), (508 bytes).
    Removing inv_mpu.o(i.mpu_read_fifo), (408 bytes).
    Removing inv_mpu.o(i.mpu_read_reg), (68 bytes).
    Removing inv_mpu.o(i.mpu_reg_dump), (88 bytes).
    Removing inv_mpu.o(i.mpu_set_accel_bias), (280 bytes).
    Removing inv_mpu.o(i.mpu_set_compass_sample_rate), (6 bytes).
    Removing inv_mpu.o(i.mpu_set_int_level), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rev16_text), (4 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.revsh_text), (4 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rrx_text), (6 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_enabled_features), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_fifo_rate), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_step_count), (56 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_walk_time), (62 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_register_android_orient_cb), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_register_tap_cb), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_interrupt_mode), (84 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_step_count), (38 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_walk_time), (44 bytes).
    Removing Direction.o(.rev16_text), (4 bytes).
    Removing Direction.o(.revsh_text), (4 bytes).
    Removing Direction.o(.rrx_text), (6 bytes).
    Removing Motor.o(.rev16_text), (4 bytes).
    Removing Motor.o(.revsh_text), (4 bytes).
    Removing Motor.o(.rrx_text), (6 bytes).
    Removing OLED.o(.rev16_text), (4 bytes).
    Removing OLED.o(.revsh_text), (4 bytes).
    Removing OLED.o(.rrx_text), (6 bytes).
    Removing OLED.o(i.OLED_ShowBinNum), (58 bytes).
    Removing OLED.o(i.OLED_ShowHexNum), (68 bytes).
    Removing fine_line.o(.rev16_text), (4 bytes).
    Removing fine_line.o(.revsh_text), (4 bytes).
    Removing fine_line.o(.rrx_text), (6 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.bss), (256 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (8 bytes).
    Removing main.o(.data), (8 bytes).
    Removing main.o(.data), (1 bytes).
    Removing pid.o(i.PI_realize), (52 bytes).
    Removing pid.o(i.P_realize), (24 bytes).
    Removing task.o(.rev16_text), (4 bytes).
    Removing task.o(.revsh_text), (4 bytes).
    Removing task.o(.rrx_text), (6 bytes).
    Removing task.o(.data), (4 bytes).

440 unused section(s) (total 35305 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llsdiv.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  aeabi_memset.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/deqf.s                          0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_noumaal.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fcheck1.s                       0x00000000   Number         0  fcheck1.o ABSOLUTE
    ../fplib/fcmpi.s                         0x00000000   Number         0  fcmpi.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/ffix.s                          0x00000000   Number         0  ffix.o ABSOLUTE
    ../fplib/ffixu.s                         0x00000000   Number         0  ffixu.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fleqf.s                         0x00000000   Number         0  fleqf.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/frleqf.s                        0x00000000   Number         0  frleqf.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/scalbnf.s                       0x00000000   Number         0  scalbnf.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    .\..\Core\Src\gpio.c                     0x00000000   Number         0  gpio.o ABSOLUTE
    .\..\Core\Src\i2c.c                      0x00000000   Number         0  i2c.o ABSOLUTE
    .\..\Core\Src\stm32f1xx_hal_msp.c        0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    .\..\Core\Src\stm32f1xx_it.c             0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    .\..\Core\Src\system_stm32f1xx.c         0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    .\..\Core\Src\tim.c                      0x00000000   Number         0  tim.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_i2c.c 0x00000000   Number         0  stm32f1xx_hal_i2c.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    .\Task\Mpu6050Dmp\MPU6050.c              0x00000000   Number         0  MPU6050.o ABSOLUTE
    .\Task\Mpu6050Dmp\inv_mpu.c              0x00000000   Number         0  inv_mpu.o ABSOLUTE
    .\Task\Mpu6050Dmp\inv_mpu_dmp_motion_driver.c 0x00000000   Number         0  inv_mpu_dmp_motion_driver.o ABSOLUTE
    .\Task\Src\Direction.c                   0x00000000   Number         0  Direction.o ABSOLUTE
    .\Task\Src\Motor.c                       0x00000000   Number         0  Motor.o ABSOLUTE
    .\Task\Src\OLED.c                        0x00000000   Number         0  OLED.o ABSOLUTE
    .\Task\Src\fine_line.c                   0x00000000   Number         0  fine_line.o ABSOLUTE
    .\Task\Src\key.c                         0x00000000   Number         0  key.o ABSOLUTE
    .\Task\Src\main.c                        0x00000000   Number         0  main.o ABSOLUTE
    .\Task\Src\pid.c                         0x00000000   Number         0  pid.o ABSOLUTE
    .\Task\Src\task.c                        0x00000000   Number         0  task.o ABSOLUTE
    .\\..\\Core\\Src\\gpio.c                 0x00000000   Number         0  gpio.o ABSOLUTE
    .\\..\\Core\\Src\\i2c.c                  0x00000000   Number         0  i2c.o ABSOLUTE
    .\\..\\Core\\Src\\stm32f1xx_hal_msp.c    0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    .\\..\\Core\\Src\\stm32f1xx_it.c         0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    .\\..\\Core\\Src\\system_stm32f1xx.c     0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    .\\..\\Core\\Src\\tim.c                  0x00000000   Number         0  tim.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_i2c.c 0x00000000   Number         0  stm32f1xx_hal_i2c.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    .\\Task\\Mpu6050Dmp\\inv_mpu.c           0x00000000   Number         0  inv_mpu.o ABSOLUTE
    .\\Task\\Mpu6050Dmp\\inv_mpu_dmp_motion_driver.c 0x00000000   Number         0  inv_mpu_dmp_motion_driver.o ABSOLUTE
    .\\Task\\Src\\Direction.c                0x00000000   Number         0  Direction.o ABSOLUTE
    .\\Task\\Src\\Motor.c                    0x00000000   Number         0  Motor.o ABSOLUTE
    .\\Task\\Src\\OLED.c                     0x00000000   Number         0  OLED.o ABSOLUTE
    .\\Task\\Src\\fine_line.c                0x00000000   Number         0  fine_line.o ABSOLUTE
    .\\Task\\Src\\key.c                      0x00000000   Number         0  key.o ABSOLUTE
    .\\Task\\Src\\main.c                     0x00000000   Number         0  main.o ABSOLUTE
    .\\Task\\Src\\task.c                     0x00000000   Number         0  task.o ABSOLUTE
    .\startup_stm32f103xb.s                  0x00000000   Number         0  startup_stm32f103xb.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f103xb.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000128   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000144   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x08000160   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000162   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000164   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000166   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08000168   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000168   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000168   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800016e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800016e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000172   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000172   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800017a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800017c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800017c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000180   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000188   Section       64  startup_stm32f103xb.o(.text)
    .text                                    0x080001c8   Section       72  llsdiv.o(.text)
    .text                                    0x08000210   Section        0  memcmp.o(.text)
    .text                                    0x08000268   Section       16  aeabi_memset.o(.text)
    .text                                    0x08000278   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080002c6   Section        0  heapauxi.o(.text)
    .text                                    0x080002cc   Section      238  lludivv7m.o(.text)
    .text                                    0x080003ba   Section        0  _rserrno.o(.text)
    .text                                    0x080003d0   Section       68  rt_memclr.o(.text)
    .text                                    0x08000414   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x0800041c   Section        8  libspace.o(.text)
    .text                                    0x08000424   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x0800046e   Section        0  exit.o(.text)
    .text                                    0x08000480   Section        0  sys_exit.o(.text)
    .text                                    0x0800048c   Section        2  use_no_semi.o(.text)
    .text                                    0x0800048e   Section        0  indicate_semi.o(.text)
    i.BusFault_Handler                       0x0800048e   Section        0  stm32f1xx_it.o(i.BusFault_Handler)
    i.ControlYawMovement                     0x08000490   Section        0  Direction.o(i.ControlYawMovement)
    i.DebugMon_Handler                       0x08000548   Section        0  stm32f1xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x0800054a   Section        0  main.o(i.Error_Handler)
    i.Find_Line_Begins                       0x08000550   Section        0  fine_line.o(i.Find_Line_Begins)
    i.HAL_Delay                              0x08000588   Section        0  stm32f1xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x080005ac   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x0800078c   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_TogglePin                     0x08000796   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    i.HAL_GPIO_WritePin                      0x080007a6   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x080007b0   Section        0  stm32f1xx_hal.o(i.HAL_GetTick)
    i.HAL_I2C_Init                           0x080007bc   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_Mem_Read                       0x08000944   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read)
    i.HAL_I2C_Mem_Write                      0x08000b94   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    i.HAL_I2C_MspInit                        0x08000cc4   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x08000d24   Section        0  stm32f1xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08000d34   Section        0  stm32f1xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08000d58   Section        0  stm32f1xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08000d98   Section        0  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08000dd4   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08000df0   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08000e30   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08000e54   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x08000f80   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetSysClockFreq                0x08000fa0   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08000fec   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x0800130c   Section        0  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_BreakCallback                0x08001334   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x08001336   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08001338   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x080013a0   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x080013fc   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x08001434   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_ConfigClockSource              0x0800148c   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_Encoder_Init                   0x08001568   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    i.HAL_TIM_Encoder_MspInit                0x0800160c   Section        0  tim.o(i.HAL_TIM_Encoder_MspInit)
    i.HAL_TIM_Encoder_Start                  0x08001660   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    i.HAL_TIM_IC_CaptureCallback             0x080016ee   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x080016f0   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_MspPostInit                    0x08001858   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_OC_DelayElapsedCallback        0x080018a0   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_ConfigChannel              0x080018a2   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x0800196e   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x080019c8   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x080019ca   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PWM_Start                      0x080019cc   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_TIM_PeriodElapsedCallback          0x08001a68   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08001a6a   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HardFault_Handler                      0x08001a6c   Section        0  stm32f1xx_it.o(i.HardFault_Handler)
    i.I2C_IsAcknowledgeFailed                0x08001a6e   Section        0  stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    I2C_IsAcknowledgeFailed                  0x08001a6f   Thumb Code    46  stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    i.I2C_RequestMemoryRead                  0x08001a9c   Section        0  stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead)
    I2C_RequestMemoryRead                    0x08001a9d   Thumb Code   246  stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead)
    i.I2C_RequestMemoryWrite                 0x08001b98   Section        0  stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    I2C_RequestMemoryWrite                   0x08001b99   Thumb Code   162  stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    i.I2C_WaitOnBTFFlagUntilTimeout          0x08001c40   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    I2C_WaitOnBTFFlagUntilTimeout            0x08001c41   Thumb Code    86  stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    i.I2C_WaitOnFlagUntilTimeout             0x08001c98   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x08001c99   Thumb Code   144  stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnMasterAddressFlagUntilTimeout 0x08001d28   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    I2C_WaitOnMasterAddressFlagUntilTimeout  0x08001d29   Thumb Code   188  stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    i.I2C_WaitOnRXNEFlagUntilTimeout         0x08001de4   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    I2C_WaitOnRXNEFlagUntilTimeout           0x08001de5   Thumb Code   112  stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    i.I2C_WaitOnTXEFlagUntilTimeout          0x08001e54   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    I2C_WaitOnTXEFlagUntilTimeout            0x08001e55   Thumb Code    86  stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    i.KEY_GetTaskIndex                       0x08001eac   Section        0  key.o(i.KEY_GetTaskIndex)
    i.KEY_Init                               0x08001eb8   Section        0  key.o(i.KEY_Init)
    i.KEY_IsSelectionMode                    0x08001ef4   Section        0  key.o(i.KEY_IsSelectionMode)
    i.KEY_IsTaskExecuted                     0x08001f00   Section        0  key.o(i.KEY_IsTaskExecuted)
    i.KEY_Scan                               0x08001f0c   Section        0  key.o(i.KEY_Scan)
    i.KEY_SetTaskExecuted                    0x08001f80   Section        0  key.o(i.KEY_SetTaskExecuted)
    i.KEY_SwitchTask                         0x08001f90   Section        0  key.o(i.KEY_SwitchTask)
    i.KEY_UpdateOLED                         0x08001fb8   Section        0  key.o(i.KEY_UpdateOLED)
    i.KEY_UpdateSelectionTimer               0x08002124   Section        0  key.o(i.KEY_UpdateSelectionTimer)
    i.MPU6050_DMP_Get_Data                   0x08002158   Section        0  MPU6050.o(i.MPU6050_DMP_Get_Data)
    i.MPU6050_DMP_Init                       0x08002328   Section        0  MPU6050.o(i.MPU6050_DMP_Init)
    i.MX_GPIO_Init                           0x080023d8   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C2_Init                           0x080024bc   Section        0  i2c.o(i.MX_I2C2_Init)
    i.MX_TIM2_Init                           0x080024fc   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM4_Init                           0x080025a4   Section        0  tim.o(i.MX_TIM4_Init)
    i.MemManage_Handler                      0x08002610   Section        0  stm32f1xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08002612   Section        0  stm32f1xx_it.o(i.NMI_Handler)
    i.OLED_Clear                             0x08002614   Section        0  OLED.o(i.OLED_Clear)
    i.OLED_I2C_Init                          0x0800263a   Section        0  OLED.o(i.OLED_I2C_Init)
    i.OLED_I2C_SendByte                      0x08002650   Section        0  OLED.o(i.OLED_I2C_SendByte)
    i.OLED_I2C_Start                         0x08002686   Section        0  OLED.o(i.OLED_I2C_Start)
    i.OLED_I2C_Stop                          0x080026a4   Section        0  OLED.o(i.OLED_I2C_Stop)
    i.OLED_Init                              0x080026bc   Section        0  OLED.o(i.OLED_Init)
    i.OLED_Pow                               0x08002768   Section        0  OLED.o(i.OLED_Pow)
    i.OLED_SetCursor                         0x08002776   Section        0  OLED.o(i.OLED_SetCursor)
    i.OLED_ShowChar                          0x08002798   Section        0  OLED.o(i.OLED_ShowChar)
    i.OLED_ShowNum                           0x08002800   Section        0  OLED.o(i.OLED_ShowNum)
    i.OLED_ShowSignedNum                     0x08002842   Section        0  OLED.o(i.OLED_ShowSignedNum)
    i.OLED_ShowString                        0x0800289a   Section        0  OLED.o(i.OLED_ShowString)
    i.OLED_W_SCL                             0x080028c0   Section        0  OLED.o(i.OLED_W_SCL)
    i.OLED_W_SDA                             0x080028d0   Section        0  OLED.o(i.OLED_W_SDA)
    i.OLED_WriteCommand                      0x080028e0   Section        0  OLED.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x08002902   Section        0  OLED.o(i.OLED_WriteData)
    i.PID_init                               0x08002924   Section        0  pid.o(i.PID_init)
    i.PID_realize                            0x08002960   Section        0  pid.o(i.PID_realize)
    i.PendSV_Handler                         0x080029b2   Section        0  stm32f1xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x080029b4   Section        0  stm32f1xx_it.o(i.SVC_Handler)
    i.SetSpeedL                              0x080029b8   Section        0  Motor.o(i.SetSpeedL)
    i.SetSpeedR                              0x080029f8   Section        0  Motor.o(i.SetSpeedR)
    i.SysTick_Handler                        0x08002a38   Section        0  stm32f1xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08002a3c   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08002a9a   Section        0  system_stm32f1xx.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x08002a9c   Section        0  stm32f1xx_it.o(i.TIM2_IRQHandler)
    i.TIM_Base_SetConfig                     0x08002aa8   Section        0  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x08002b14   Section        0  stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_ETR_SetConfig                      0x08002b2e   Section        0  stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x08002b42   Section        0  stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x08002b43   Thumb Code    16  stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_OC1_SetConfig                      0x08002b54   Section        0  stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x08002b55   Thumb Code    74  stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x08002ba4   Section        0  stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x08002bfc   Section        0  stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x08002bfd   Thumb Code    82  stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x08002c54   Section        0  stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x08002c55   Thumb Code    64  stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x08002c98   Section        0  stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x08002c99   Thumb Code    34  stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x08002cba   Section        0  stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08002cbb   Thumb Code    36  stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UsageFault_Handler                     0x08002cde   Section        0  stm32f1xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08002ce0   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__NVIC_SetPriority                     0x08002d08   Section        0  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08002d09   Thumb Code    32  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__kernel_poly                          0x08002d28   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_infnan                   0x08002dd2   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_infnan2                  0x08002dd8   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x08002ddc   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_underflow                0x08002de8   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.abds                                   0x08002df8   Section        0  Motor.o(i.abds)
    abds                                     0x08002df9   Thumb Code     8  Motor.o(i.abds)
    i.accel_self_test                        0x08002e00   Section        0  inv_mpu.o(i.accel_self_test)
    accel_self_test                          0x08002e01   Thumb Code   114  inv_mpu.o(i.accel_self_test)
    i.asin                                   0x08002e80   Section        0  asin.o(i.asin)
    i.atan                                   0x080030f0   Section        0  atan.o(i.atan)
    i.atan2                                  0x08003310   Section        0  atan2.o(i.atan2)
    i.dmp_enable_6x_lp_quat                  0x080034ac   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat)
    i.dmp_enable_feature                     0x080034e8   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature)
    i.dmp_enable_gyro_cal                    0x080036c0   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal)
    i.dmp_enable_lp_quat                     0x080036fc   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat)
    i.dmp_load_motion_driver_firmware        0x08003738   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware)
    i.dmp_read_fifo                          0x0800374c   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo)
    i.dmp_set_accel_bias                     0x080038b4   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias)
    i.dmp_set_fifo_rate                      0x0800398c   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate)
    i.dmp_set_gyro_bias                      0x080039f0   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias)
    i.dmp_set_orientation                    0x08003abc   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation)
    i.dmp_set_shake_reject_thresh            0x08003bc8   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh)
    i.dmp_set_shake_reject_time              0x08003bf8   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time)
    i.dmp_set_shake_reject_timeout           0x08003c18   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout)
    i.dmp_set_tap_axes                       0x08003c38   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes)
    i.dmp_set_tap_count                      0x08003c78   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count)
    i.dmp_set_tap_thresh                     0x08003c9c   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh)
    i.dmp_set_tap_time                       0x08003dc4   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time)
    i.dmp_set_tap_time_multi                 0x08003de4   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi)
    i.get_LedFind_Scan                       0x08003e04   Section        0  fine_line.o(i.get_LedFind_Scan)
    i.get_accel_prod_shift                   0x08003e78   Section        0  inv_mpu.o(i.get_accel_prod_shift)
    get_accel_prod_shift                     0x08003e79   Thumb Code   174  inv_mpu.o(i.get_accel_prod_shift)
    i.get_st_biases                          0x08003f38   Section        0  inv_mpu.o(i.get_st_biases)
    get_st_biases                            0x08003f39   Thumb Code  1086  inv_mpu.o(i.get_st_biases)
    i.gyro_self_test                         0x08004378   Section        0  inv_mpu.o(i.gyro_self_test)
    gyro_self_test                           0x08004379   Thumb Code   202  inv_mpu.o(i.gyro_self_test)
    i.inv_row_2_scale                        0x0800445c   Section        0  MPU6050.o(i.inv_row_2_scale)
    inv_row_2_scale                          0x0800445d   Thumb Code    72  MPU6050.o(i.inv_row_2_scale)
    i.line_detect                            0x080044a4   Section        0  fine_line.o(i.line_detect)
    i.main                                   0x080044cc   Section        0  main.o(i.main)
    i.mpu_configure_fifo                     0x08004598   Section        0  inv_mpu.o(i.mpu_configure_fifo)
    i.mpu_get_accel_fsr                      0x080045ec   Section        0  inv_mpu.o(i.mpu_get_accel_fsr)
    i.mpu_get_accel_sens                     0x08004628   Section        0  inv_mpu.o(i.mpu_get_accel_sens)
    i.mpu_get_gyro_fsr                       0x0800466c   Section        0  inv_mpu.o(i.mpu_get_gyro_fsr)
    i.mpu_get_gyro_sens                      0x080046a0   Section        0  inv_mpu.o(i.mpu_get_gyro_sens)
    i.mpu_get_lpf                            0x080046e0   Section        0  inv_mpu.o(i.mpu_get_lpf)
    i.mpu_get_sample_rate                    0x08004718   Section        0  inv_mpu.o(i.mpu_get_sample_rate)
    i.mpu_init                               0x08004734   Section        0  inv_mpu.o(i.mpu_init)
    i.mpu_load_firmware                      0x0800488c   Section        0  inv_mpu.o(i.mpu_load_firmware)
    i.mpu_lp_accel_mode                      0x08004948   Section        0  inv_mpu.o(i.mpu_lp_accel_mode)
    i.mpu_read_fifo_stream                   0x08004a2c   Section        0  inv_mpu.o(i.mpu_read_fifo_stream)
    i.mpu_read_mem                           0x08004af0   Section        0  inv_mpu.o(i.mpu_read_mem)
    i.mpu_reset_fifo                         0x08004b64   Section        0  inv_mpu.o(i.mpu_reset_fifo)
    i.mpu_run_self_test                      0x08004d34   Section        0  inv_mpu.o(i.mpu_run_self_test)
    i.mpu_set_accel_fsr                      0x08004e24   Section        0  inv_mpu.o(i.mpu_set_accel_fsr)
    i.mpu_set_bypass                         0x08004ea0   Section        0  inv_mpu.o(i.mpu_set_bypass)
    i.mpu_set_dmp_state                      0x08004fe8   Section        0  inv_mpu.o(i.mpu_set_dmp_state)
    i.mpu_set_gyro_fsr                       0x0800507c   Section        0  inv_mpu.o(i.mpu_set_gyro_fsr)
    i.mpu_set_int_latched                    0x08005100   Section        0  inv_mpu.o(i.mpu_set_int_latched)
    i.mpu_set_lpf                            0x08005178   Section        0  inv_mpu.o(i.mpu_set_lpf)
    i.mpu_set_sample_rate                    0x080051f4   Section        0  inv_mpu.o(i.mpu_set_sample_rate)
    i.mpu_set_sensors                        0x0800528c   Section        0  inv_mpu.o(i.mpu_set_sensors)
    i.mpu_write_mem                          0x0800536c   Section        0  inv_mpu.o(i.mpu_write_mem)
    i.run_self_test                          0x080053e0   Section        0  MPU6050.o(i.run_self_test)
    run_self_test                            0x080053e1   Thumb Code   130  MPU6050.o(i.run_self_test)
    i.set_int_enable                         0x08005464   Section        0  inv_mpu.o(i.set_int_enable)
    set_int_enable                           0x08005465   Thumb Code    98  inv_mpu.o(i.set_int_enable)
    i.sqrt                                   0x080054d0   Section        0  sqrt.o(i.sqrt)
    i.task_0                                 0x0800551c   Section        0  task.o(i.task_0)
    i.task_1                                 0x08005544   Section        0  task.o(i.task_1)
    i.task_2                                 0x080056a0   Section        0  task.o(i.task_2)
    i.task_3                                 0x08005a24   Section        0  task.o(i.task_3)
    i.task_4                                 0x08005c2c   Section        0  task.o(i.task_4)
    x$fpl$d2f                                0x08005e04   Section       98  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x08005e68   Section      336  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x08005e79   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcheck1                            0x08005fb8   Section       16  dcheck1.o(x$fpl$dcheck1)
    x$fpl$dcmpinf                            0x08005fc8   Section       24  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x08005fe0   Section      688  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x08005fe7   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$deqf                               0x08006290   Section      120  deqf.o(x$fpl$deqf)
    x$fpl$dmul                               0x08006308   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x0800645c   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x080064f8   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$drsb                               0x08006504   Section       22  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsqrt                              0x0800651c   Section      460  dsqrt_noumaal.o(x$fpl$dsqrt)
    x$fpl$dsub                               0x080066e8   Section      468  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x080066f9   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x080068bc   Section       86  f2d.o(x$fpl$f2d)
    x$fpl$fadd                               0x08006914   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x08006923   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fcheck1                            0x080069d8   Section       12  fcheck1.o(x$fpl$fcheck1)
    x$fpl$fcmpinf                            0x080069e4   Section       24  fcmpi.o(x$fpl$fcmpinf)
    x$fpl$fdiv                               0x080069fc   Section      388  fdiv.o(x$fpl$fdiv)
    _fdiv1                                   0x080069fd   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$ffix                               0x08006b80   Section       54  ffix.o(x$fpl$ffix)
    x$fpl$ffixu                              0x08006bb8   Section       62  ffixu.o(x$fpl$ffixu)
    x$fpl$fflt                               0x08006bf8   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$ffltu                              0x08006c28   Section       38  fflt_clz.o(x$fpl$ffltu)
    x$fpl$fleqf                              0x08006c50   Section      104  fleqf.o(x$fpl$fleqf)
    x$fpl$fmul                               0x08006cb8   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x08006dba   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x08006e46   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$frleqf                             0x08006e50   Section       98  frleqf.o(x$fpl$frleqf)
    x$fpl$fsub                               0x08006eb4   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x08006ec3   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    x$fpl$retnan                             0x08006f9e   Section      100  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x08007002   Section       92  scalbn.o(x$fpl$scalbn)
    x$fpl$scalbnf                            0x0800705e   Section       76  scalbnf.o(x$fpl$scalbnf)
    x$fpl$trapveneer                         0x080070aa   Section       48  trapv.o(x$fpl$trapveneer)
    .constdata                               0x080070da   Section       16  system_stm32f1xx.o(.constdata)
    x$fpl$usenofp                            0x080070da   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x080070ea   Section        8  system_stm32f1xx.o(.constdata)
    .constdata                               0x080070f2   Section       18  stm32f1xx_hal_rcc.o(.constdata)
    aPredivFactorTable                       0x080070f2   Data           2  stm32f1xx_hal_rcc.o(.constdata)
    aPLLMULFactorTable                       0x080070f4   Data          16  stm32f1xx_hal_rcc.o(.constdata)
    .constdata                               0x08007104   Section       30  inv_mpu.o(.constdata)
    .constdata                               0x08007122   Section       12  inv_mpu.o(.constdata)
    .constdata                               0x08007130   Section       40  inv_mpu.o(.constdata)
    .constdata                               0x08007158   Section     3062  inv_mpu_dmp_motion_driver.o(.constdata)
    dmp_memory                               0x08007158   Data        3062  inv_mpu_dmp_motion_driver.o(.constdata)
    .constdata                               0x08007d4e   Section     1520  OLED.o(.constdata)
    .constdata                               0x08008340   Section       80  asin.o(.constdata)
    pS                                       0x08008340   Data          48  asin.o(.constdata)
    qS                                       0x08008370   Data          32  asin.o(.constdata)
    .constdata                               0x08008390   Section      152  atan.o(.constdata)
    atanhi                                   0x08008390   Data          32  atan.o(.constdata)
    atanlo                                   0x080083b0   Data          32  atan.o(.constdata)
    aTodd                                    0x080083d0   Data          40  atan.o(.constdata)
    aTeven                                   0x080083f8   Data          48  atan.o(.constdata)
    .constdata                               0x08008428   Section        8  qnan.o(.constdata)
    .data                                    0x20000000   Section        4  system_stm32f1xx.o(.data)
    .data                                    0x20000004   Section       12  stm32f1xx_hal.o(.data)
    .data                                    0x20000010   Section        9  MPU6050.o(.data)
    gyro_orientation                         0x20000010   Data           9  MPU6050.o(.data)
    .data                                    0x2000001c   Section       44  inv_mpu.o(.data)
    st                                       0x2000001c   Data          44  inv_mpu.o(.data)
    .data                                    0x20000048   Section      100  fine_line.o(.data)
    Linechange_list                          0x2000008c   Data          32  fine_line.o(.data)
    .data                                    0x200000ac   Section       12  key.o(.data)
    g_taskIndex                              0x200000ac   Data           1  key.o(.data)
    g_isSelectionMode                        0x200000ad   Data           1  key.o(.data)
    g_taskExecuted                           0x200000ae   Data           1  key.o(.data)
    key_up                                   0x200000af   Data           1  key.o(.data)
    g_selectionTimer                         0x200000b0   Data           4  key.o(.data)
    long_press_count                         0x200000b4   Data           4  key.o(.data)
    .data                                    0x200000b8   Section        4  main.o(.data)
    .data                                    0x200000bc   Section        4  main.o(.data)
    .data                                    0x200000c0   Section       40  task.o(.data)
    speed_set                                0x200000c1   Data           1  task.o(.data)
    case2_speed_set                          0x200000c2   Data           1  task.o(.data)
    case4_speed_set                          0x200000c3   Data           1  task.o(.data)
    task4_cycle_count                        0x200000c4   Data           1  task.o(.data)
    task4_initialized                        0x200000c5   Data           1  task.o(.data)
    detect_count                             0x200000c6   Data           2  task.o(.data)
    .bss                                     0x200000e8   Section       84  i2c.o(.bss)
    .bss                                     0x2000013c   Section      144  tim.o(.bss)
    .bss                                     0x200001cc   Section       16  inv_mpu_dmp_motion_driver.o(.bss)
    dmp                                      0x200001cc   Data          16  inv_mpu_dmp_motion_driver.o(.bss)
    .bss                                     0x200001dc   Section       20  fine_line.o(.bss)
    .bss                                     0x200001f0   Section       64  pid.o(.bss)
    .bss                                     0x20000230   Section       96  libspace.o(.bss)
    HEAP                                     0x20000290   Section      512  startup_stm32f103xb.o(HEAP)
    Heap_Mem                                 0x20000290   Data         512  startup_stm32f103xb.o(HEAP)
    STACK                                    0x20000490   Section     1024  startup_stm32f103xb.o(STACK)
    Stack_Mem                                0x20000490   Data        1024  startup_stm32f103xb.o(STACK)
    __initial_sp                             0x20000890   Data           0  startup_stm32f103xb.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f103xb.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f103xb.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f103xb.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000129   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000145   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x08000161   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000165   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08000169   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000169   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000169   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800016f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800016f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800017b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800017d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800017d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000181   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000189   Thumb Code     8  startup_stm32f103xb.o(.text)
    ADC1_2_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_RX1_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_SCE_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI0_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI15_10_IRQHandler                     0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI1_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI2_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI3_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI4_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI9_5_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    FLASH_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_ER_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_EV_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_ER_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_EV_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    PVD_IRQHandler                           0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    RCC_IRQHandler                           0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_Alarm_IRQHandler                     0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_IRQHandler                           0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI1_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI2_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TAMPER_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_BRK_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_CC_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_UP_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM3_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM4_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART1_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART2_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART3_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    USBWakeUp_IRQHandler                     0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    WWDG_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f103xb.o(.text)
    __user_initial_stackheap                 0x080001a5   Thumb Code     0  startup_stm32f103xb.o(.text)
    __aeabi_ldivmod                          0x080001c9   Thumb Code     0  llsdiv.o(.text)
    _ll_sdiv                                 0x080001c9   Thumb Code    72  llsdiv.o(.text)
    memcmp                                   0x08000211   Thumb Code    88  memcmp.o(.text)
    __aeabi_memset                           0x08000269   Thumb Code    16  aeabi_memset.o(.text)
    __aeabi_memclr4                          0x08000279   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000279   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000279   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x0800027d   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x080002c7   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080002c9   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080002cb   Thumb Code     2  heapauxi.o(.text)
    __aeabi_uldivmod                         0x080002cd   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x080002cd   Thumb Code   238  lludivv7m.o(.text)
    __read_errno                             0x080003bb   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x080003c5   Thumb Code    12  _rserrno.o(.text)
    __aeabi_memclr                           0x080003d1   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x080003d1   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x080003d5   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_errno_addr                       0x08000415   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000415   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000415   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __user_libspace                          0x0800041d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x0800041d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x0800041d   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08000425   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x0800046f   Thumb Code    18  exit.o(.text)
    _sys_exit                                0x08000481   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x0800048d   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0800048d   Thumb Code     2  use_no_semi.o(.text)
    BusFault_Handler                         0x0800048f   Thumb Code     2  stm32f1xx_it.o(i.BusFault_Handler)
    __semihosting_library_function           0x0800048f   Thumb Code     0  indicate_semi.o(.text)
    ControlYawMovement                       0x08000491   Thumb Code   166  Direction.o(i.ControlYawMovement)
    DebugMon_Handler                         0x08000549   Thumb Code     2  stm32f1xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x0800054b   Thumb Code     4  main.o(i.Error_Handler)
    Find_Line_Begins                         0x08000551   Thumb Code    46  fine_line.o(i.Find_Line_Begins)
    HAL_Delay                                0x08000589   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x080005ad   Thumb Code   446  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x0800078d   Thumb Code    10  stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_TogglePin                       0x08000797   Thumb Code    16  stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    HAL_GPIO_WritePin                        0x080007a7   Thumb Code    10  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080007b1   Thumb Code     6  stm32f1xx_hal.o(i.HAL_GetTick)
    HAL_I2C_Init                             0x080007bd   Thumb Code   376  stm32f1xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_Mem_Read                         0x08000945   Thumb Code   574  stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read)
    HAL_I2C_Mem_Write                        0x08000b95   Thumb Code   294  stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    HAL_I2C_MspInit                          0x08000cc5   Thumb Code    82  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x08000d25   Thumb Code    12  stm32f1xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08000d35   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08000d59   Thumb Code    54  stm32f1xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08000d99   Thumb Code    52  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08000dd5   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08000df1   Thumb Code    60  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08000e31   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08000e55   Thumb Code   280  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08000f81   Thumb Code    20  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetSysClockFreq                  0x08000fa1   Thumb Code    58  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08000fed   Thumb Code   778  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x0800130d   Thumb Code    40  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_BreakCallback                  0x08001335   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x08001337   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_MasterConfigSynchronization    0x08001339   Thumb Code    92  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x080013a1   Thumb Code    90  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x080013fd   Thumb Code    50  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08001435   Thumb Code    76  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_ConfigClockSource                0x0800148d   Thumb Code   220  stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x08001569   Thumb Code   164  stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x0800160d   Thumb Code    72  tim.o(i.HAL_TIM_Encoder_MspInit)
    HAL_TIM_Encoder_Start                    0x08001661   Thumb Code   142  stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    HAL_TIM_IC_CaptureCallback               0x080016ef   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x080016f1   Thumb Code   358  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_MspPostInit                      0x08001859   Thumb Code    62  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_OC_DelayElapsedCallback          0x080018a1   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x080018a3   Thumb Code   204  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x0800196f   Thumb Code    90  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x080019c9   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x080019cb   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PWM_Start                        0x080019cd   Thumb Code   144  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_TIM_PeriodElapsedCallback            0x08001a69   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08001a6b   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HardFault_Handler                        0x08001a6d   Thumb Code     2  stm32f1xx_it.o(i.HardFault_Handler)
    KEY_GetTaskIndex                         0x08001ead   Thumb Code     6  key.o(i.KEY_GetTaskIndex)
    KEY_Init                                 0x08001eb9   Thumb Code    52  key.o(i.KEY_Init)
    KEY_IsSelectionMode                      0x08001ef5   Thumb Code     6  key.o(i.KEY_IsSelectionMode)
    KEY_IsTaskExecuted                       0x08001f01   Thumb Code     6  key.o(i.KEY_IsTaskExecuted)
    KEY_Scan                                 0x08001f0d   Thumb Code   106  key.o(i.KEY_Scan)
    KEY_SetTaskExecuted                      0x08001f81   Thumb Code    10  key.o(i.KEY_SetTaskExecuted)
    KEY_SwitchTask                           0x08001f91   Thumb Code    34  key.o(i.KEY_SwitchTask)
    KEY_UpdateOLED                           0x08001fb9   Thumb Code   196  key.o(i.KEY_UpdateOLED)
    KEY_UpdateSelectionTimer                 0x08002125   Thumb Code    46  key.o(i.KEY_UpdateSelectionTimer)
    MPU6050_DMP_Get_Data                     0x08002159   Thumb Code   460  MPU6050.o(i.MPU6050_DMP_Get_Data)
    MPU6050_DMP_Init                         0x08002329   Thumb Code   172  MPU6050.o(i.MPU6050_DMP_Init)
    MX_GPIO_Init                             0x080023d9   Thumb Code   216  gpio.o(i.MX_GPIO_Init)
    MX_I2C2_Init                             0x080024bd   Thumb Code    50  i2c.o(i.MX_I2C2_Init)
    MX_TIM2_Init                             0x080024fd   Thumb Code   162  tim.o(i.MX_TIM2_Init)
    MX_TIM4_Init                             0x080025a5   Thumb Code   100  tim.o(i.MX_TIM4_Init)
    MemManage_Handler                        0x08002611   Thumb Code     2  stm32f1xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08002613   Thumb Code     2  stm32f1xx_it.o(i.NMI_Handler)
    OLED_Clear                               0x08002615   Thumb Code    38  OLED.o(i.OLED_Clear)
    OLED_I2C_Init                            0x0800263b   Thumb Code    22  OLED.o(i.OLED_I2C_Init)
    OLED_I2C_SendByte                        0x08002651   Thumb Code    54  OLED.o(i.OLED_I2C_SendByte)
    OLED_I2C_Start                           0x08002687   Thumb Code    30  OLED.o(i.OLED_I2C_Start)
    OLED_I2C_Stop                            0x080026a5   Thumb Code    24  OLED.o(i.OLED_I2C_Stop)
    OLED_Init                                0x080026bd   Thumb Code   172  OLED.o(i.OLED_Init)
    OLED_Pow                                 0x08002769   Thumb Code    14  OLED.o(i.OLED_Pow)
    OLED_SetCursor                           0x08002777   Thumb Code    34  OLED.o(i.OLED_SetCursor)
    OLED_ShowChar                            0x08002799   Thumb Code    98  OLED.o(i.OLED_ShowChar)
    OLED_ShowNum                             0x08002801   Thumb Code    66  OLED.o(i.OLED_ShowNum)
    OLED_ShowSignedNum                       0x08002843   Thumb Code    88  OLED.o(i.OLED_ShowSignedNum)
    OLED_ShowString                          0x0800289b   Thumb Code    38  OLED.o(i.OLED_ShowString)
    OLED_W_SCL                               0x080028c1   Thumb Code    12  OLED.o(i.OLED_W_SCL)
    OLED_W_SDA                               0x080028d1   Thumb Code    12  OLED.o(i.OLED_W_SDA)
    OLED_WriteCommand                        0x080028e1   Thumb Code    34  OLED.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x08002903   Thumb Code    34  OLED.o(i.OLED_WriteData)
    PID_init                                 0x08002925   Thumb Code    46  pid.o(i.PID_init)
    PID_realize                              0x08002961   Thumb Code    82  pid.o(i.PID_realize)
    PendSV_Handler                           0x080029b3   Thumb Code     2  stm32f1xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x080029b5   Thumb Code     2  stm32f1xx_it.o(i.SVC_Handler)
    SetSpeedL                                0x080029b9   Thumb Code    56  Motor.o(i.SetSpeedL)
    SetSpeedR                                0x080029f9   Thumb Code    56  Motor.o(i.SetSpeedR)
    SysTick_Handler                          0x08002a39   Thumb Code     4  stm32f1xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08002a3d   Thumb Code    94  main.o(i.SystemClock_Config)
    SystemInit                               0x08002a9b   Thumb Code     2  system_stm32f1xx.o(i.SystemInit)
    TIM2_IRQHandler                          0x08002a9d   Thumb Code     6  stm32f1xx_it.o(i.TIM2_IRQHandler)
    TIM_Base_SetConfig                       0x08002aa9   Thumb Code    94  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x08002b15   Thumb Code    26  stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_ETR_SetConfig                        0x08002b2f   Thumb Code    20  stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_OC2_SetConfig                        0x08002ba5   Thumb Code    84  stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UsageFault_Handler                       0x08002cdf   Thumb Code     2  stm32f1xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08002ce1   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    __kernel_poly                            0x08002d29   Thumb Code   170  poly.o(i.__kernel_poly)
    __mathlib_dbl_infnan                     0x08002dd3   Thumb Code     6  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_infnan2                    0x08002dd9   Thumb Code     4  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x08002ddd   Thumb Code    12  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_underflow                  0x08002de9   Thumb Code    10  dunder.o(i.__mathlib_dbl_underflow)
    asin                                     0x08002e81   Thumb Code   572  asin.o(i.asin)
    atan                                     0x080030f1   Thumb Code   474  atan.o(i.atan)
    atan2                                    0x08003311   Thumb Code   374  atan2.o(i.atan2)
    dmp_enable_6x_lp_quat                    0x080034ad   Thumb Code    58  inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat)
    dmp_enable_feature                       0x080034e9   Thumb Code   464  inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature)
    dmp_enable_gyro_cal                      0x080036c1   Thumb Code    34  inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal)
    dmp_enable_lp_quat                       0x080036fd   Thumb Code    58  inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat)
    dmp_load_motion_driver_firmware          0x08003739   Thumb Code    16  inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware)
    dmp_read_fifo                            0x0800374d   Thumb Code   354  inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo)
    dmp_set_accel_bias                       0x080038b5   Thumb Code   212  inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias)
    dmp_set_fifo_rate                        0x0800398d   Thumb Code    82  inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate)
    dmp_set_gyro_bias                        0x080039f1   Thumb Code   196  inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias)
    dmp_set_orientation                      0x08003abd   Thumb Code   246  inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation)
    dmp_set_shake_reject_thresh              0x08003bc9   Thumb Code    48  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh)
    dmp_set_shake_reject_time                0x08003bf9   Thumb Code    32  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time)
    dmp_set_shake_reject_timeout             0x08003c19   Thumb Code    32  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout)
    dmp_set_tap_axes                         0x08003c39   Thumb Code    64  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes)
    dmp_set_tap_count                        0x08003c79   Thumb Code    34  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count)
    dmp_set_tap_thresh                       0x08003c9d   Thumb Code   274  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh)
    dmp_set_tap_time                         0x08003dc5   Thumb Code    32  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time)
    dmp_set_tap_time_multi                   0x08003de5   Thumb Code    32  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi)
    get_LedFind_Scan                         0x08003e05   Thumb Code   110  fine_line.o(i.get_LedFind_Scan)
    line_detect                              0x080044a5   Thumb Code    34  fine_line.o(i.line_detect)
    main                                     0x080044cd   Thumb Code   188  main.o(i.main)
    mpu_configure_fifo                       0x08004599   Thumb Code    78  inv_mpu.o(i.mpu_configure_fifo)
    mpu_get_accel_fsr                        0x080045ed   Thumb Code    54  inv_mpu.o(i.mpu_get_accel_fsr)
    mpu_get_accel_sens                       0x08004629   Thumb Code    62  inv_mpu.o(i.mpu_get_accel_sens)
    mpu_get_gyro_fsr                         0x0800466d   Thumb Code    48  inv_mpu.o(i.mpu_get_gyro_fsr)
    mpu_get_gyro_sens                        0x080046a1   Thumb Code    44  inv_mpu.o(i.mpu_get_gyro_sens)
    mpu_get_lpf                              0x080046e1   Thumb Code    52  inv_mpu.o(i.mpu_get_lpf)
    mpu_get_sample_rate                      0x08004719   Thumb Code    22  inv_mpu.o(i.mpu_get_sample_rate)
    mpu_init                                 0x08004735   Thumb Code   334  inv_mpu.o(i.mpu_init)
    mpu_load_firmware                        0x0800488d   Thumb Code   178  inv_mpu.o(i.mpu_load_firmware)
    mpu_lp_accel_mode                        0x08004949   Thumb Code   220  inv_mpu.o(i.mpu_lp_accel_mode)
    mpu_read_fifo_stream                     0x08004a2d   Thumb Code   188  inv_mpu.o(i.mpu_read_fifo_stream)
    mpu_read_mem                             0x08004af1   Thumb Code   106  inv_mpu.o(i.mpu_read_mem)
    mpu_reset_fifo                           0x08004b65   Thumb Code   454  inv_mpu.o(i.mpu_reset_fifo)
    mpu_run_self_test                        0x08004d35   Thumb Code   234  inv_mpu.o(i.mpu_run_self_test)
    mpu_set_accel_fsr                        0x08004e25   Thumb Code   116  inv_mpu.o(i.mpu_set_accel_fsr)
    mpu_set_bypass                           0x08004ea1   Thumb Code   320  inv_mpu.o(i.mpu_set_bypass)
    mpu_set_dmp_state                        0x08004fe9   Thumb Code   140  inv_mpu.o(i.mpu_set_dmp_state)
    mpu_set_gyro_fsr                         0x0800507d   Thumb Code   122  inv_mpu.o(i.mpu_set_gyro_fsr)
    mpu_set_int_latched                      0x08005101   Thumb Code   110  inv_mpu.o(i.mpu_set_int_latched)
    mpu_set_lpf                              0x08005179   Thumb Code   116  inv_mpu.o(i.mpu_set_lpf)
    mpu_set_sample_rate                      0x080051f5   Thumb Code   142  inv_mpu.o(i.mpu_set_sample_rate)
    mpu_set_sensors                          0x0800528d   Thumb Code   214  inv_mpu.o(i.mpu_set_sensors)
    mpu_write_mem                            0x0800536d   Thumb Code   106  inv_mpu.o(i.mpu_write_mem)
    sqrt                                     0x080054d1   Thumb Code    76  sqrt.o(i.sqrt)
    task_0                                   0x0800551d   Thumb Code    38  task.o(i.task_0)
    task_1                                   0x08005545   Thumb Code   318  task.o(i.task_1)
    task_2                                   0x080056a1   Thumb Code   740  task.o(i.task_2)
    task_3                                   0x08005a25   Thumb Code   468  task.o(i.task_3)
    task_4                                   0x08005c2d   Thumb Code   334  task.o(i.task_4)
    __aeabi_d2f                              0x08005e05   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08005e05   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x08005e69   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x08005e69   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcheck_NaN1                        0x08005fb9   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __fpl_dcmp_Inf                           0x08005fc9   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x08005fe1   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08005fe1   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_cdcmpeq                          0x08006291   Thumb Code     0  deqf.o(x$fpl$deqf)
    _dcmpeq                                  0x08006291   Thumb Code   120  deqf.o(x$fpl$deqf)
    __aeabi_dmul                             0x08006309   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08006309   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x0800645d   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x080064f9   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_drsub                            0x08006505   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x08006505   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    _dsqrt                                   0x0800651d   Thumb Code   456  dsqrt_noumaal.o(x$fpl$dsqrt)
    __aeabi_dsub                             0x080066e9   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x080066e9   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x080068bd   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x080068bd   Thumb Code    86  f2d.o(x$fpl$f2d)
    __aeabi_fadd                             0x08006915   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x08006915   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __fpl_fcheck_NaN1                        0x080069d9   Thumb Code     6  fcheck1.o(x$fpl$fcheck1)
    __fpl_fcmp_Inf                           0x080069e5   Thumb Code    24  fcmpi.o(x$fpl$fcmpinf)
    __aeabi_fdiv                             0x080069fd   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x080069fd   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_f2iz                             0x08006b81   Thumb Code     0  ffix.o(x$fpl$ffix)
    _ffix                                    0x08006b81   Thumb Code    54  ffix.o(x$fpl$ffix)
    __aeabi_f2uiz                            0x08006bb9   Thumb Code     0  ffixu.o(x$fpl$ffixu)
    _ffixu                                   0x08006bb9   Thumb Code    62  ffixu.o(x$fpl$ffixu)
    __aeabi_i2f                              0x08006bf9   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x08006bf9   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __aeabi_ui2f                             0x08006c29   Thumb Code     0  fflt_clz.o(x$fpl$ffltu)
    _ffltu                                   0x08006c29   Thumb Code    38  fflt_clz.o(x$fpl$ffltu)
    __aeabi_cfcmple                          0x08006c51   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    _fcmple                                  0x08006c51   Thumb Code   104  fleqf.o(x$fpl$fleqf)
    __fpl_fcmple_InfNaN                      0x08006ca3   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    __aeabi_fmul                             0x08006cb9   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x08006cb9   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x08006dbb   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x08006e47   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_cfrcmple                         0x08006e51   Thumb Code     0  frleqf.o(x$fpl$frleqf)
    _frcmple                                 0x08006e51   Thumb Code    98  frleqf.o(x$fpl$frleqf)
    __aeabi_fsub                             0x08006eb5   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x08006eb5   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    __fpl_return_NaN                         0x08006f9f   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x08007003   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    __ARM_scalbnf                            0x0800705f   Thumb Code    76  scalbnf.o(x$fpl$scalbnf)
    __fpl_cmpreturn                          0x080070ab   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    AHBPrescTable                            0x080070da   Data          16  system_stm32f1xx.o(.constdata)
    __I$use$fp                               0x080070da   Number         0  usenofp.o(x$fpl$usenofp)
    APBPrescTable                            0x080070ea   Data           8  system_stm32f1xx.o(.constdata)
    reg                                      0x08007104   Data          30  inv_mpu.o(.constdata)
    hw                                       0x08007122   Data          12  inv_mpu.o(.constdata)
    test                                     0x08007130   Data          40  inv_mpu.o(.constdata)
    OLED_F8x16                               0x08007d4e   Data        1520  OLED.o(.constdata)
    __mathlib_zero                           0x08008428   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x08008430   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08008450   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f1xx.o(.data)
    uwTickFreq                               0x20000004   Data           1  stm32f1xx_hal.o(.data)
    uwTickPrio                               0x20000008   Data           4  stm32f1xx_hal.o(.data)
    uwTick                                   0x2000000c   Data           4  stm32f1xx_hal.o(.data)
    have_line                                0x20000048   Data           1  fine_line.o(.data)
    linepin_list                             0x2000004c   Data          64  fine_line.o(.data)
    g_fMPU6050YawMovePidOut1                 0x200000b8   Data           4  main.o(.data)
    g_fMPU6050YawMovePidOut2                 0x200000bc   Data           4  main.o(.data)
    Do_count                                 0x200000c0   Data           1  task.o(.data)
    gx                                       0x200000c8   Data           2  task.o(.data)
    gy                                       0x200000ca   Data           2  task.o(.data)
    gz                                       0x200000cc   Data           2  task.o(.data)
    ax                                       0x200000ce   Data           2  task.o(.data)
    ay                                       0x200000d0   Data           2  task.o(.data)
    az                                       0x200000d2   Data           2  task.o(.data)
    findline_time                            0x200000d4   Data           4  task.o(.data)
    pitch                                    0x200000d8   Data           4  task.o(.data)
    roll                                     0x200000dc   Data           4  task.o(.data)
    yaw                                      0x200000e0   Data           4  task.o(.data)
    lose_time                                0x200000e4   Data           4  task.o(.data)
    hi2c2                                    0x200000e8   Data          84  i2c.o(.bss)
    htim2                                    0x2000013c   Data          72  tim.o(.bss)
    htim4                                    0x20000184   Data          72  tim.o(.bss)
    value_led                                0x200001dc   Data          20  fine_line.o(.bss)
    pidMotor1Speed                           0x200001f0   Data          32  pid.o(.bss)
    pidMPU6050YawMovement                    0x20000210   Data          32  pid.o(.bss)
    __libspace_start                         0x20000230   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000290   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00008538, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Base: 0x08000000, Size: 0x00008450, Max: 0x00010000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x000000ec   Data   RO         3807    RESET               startup_stm32f103xb.o
    0x080000ec   0x00000008   Code   RO         3826  * !!!main             c_w.l(__main.o)
    0x080000f4   0x00000034   Code   RO         4138    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x0000001a   Code   RO         4140    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000142   0x00000002   PAD
    0x08000144   0x0000001c   Code   RO         4142    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000160   0x00000002   Code   RO         4003    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000162   0x00000000   Code   RO         4013    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4015    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4018    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4020    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4022    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4025    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4027    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4029    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4031    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4033    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4035    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4037    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4039    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4041    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4043    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4045    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4049    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4051    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4053    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4055    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000162   0x00000002   Code   RO         4056    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000164   0x00000002   Code   RO         4086    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000166   0x00000000   Code   RO         4103    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000166   0x00000000   Code   RO         4105    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000166   0x00000000   Code   RO         4107    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000166   0x00000000   Code   RO         4110    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000166   0x00000000   Code   RO         4113    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000166   0x00000000   Code   RO         4115    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000166   0x00000000   Code   RO         4118    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000166   0x00000002   Code   RO         4119    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08000168   0x00000000   Code   RO         3882    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000168   0x00000000   Code   RO         3954    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000168   0x00000006   Code   RO         3966    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800016e   0x00000000   Code   RO         3956    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800016e   0x00000004   Code   RO         3957    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000172   0x00000000   Code   RO         3959    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000172   0x00000008   Code   RO         3960    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800017a   0x00000002   Code   RO         4006    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800017c   0x00000000   Code   RO         4058    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800017c   0x00000004   Code   RO         4059    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000180   0x00000006   Code   RO         4060    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000186   0x00000002   PAD
    0x08000188   0x00000040   Code   RO         3808    .text               startup_stm32f103xb.o
    0x080001c8   0x00000048   Code   RO         3816    .text               c_w.l(llsdiv.o)
    0x08000210   0x00000058   Code   RO         3818    .text               c_w.l(memcmp.o)
    0x08000268   0x00000010   Code   RO         3820    .text               c_w.l(aeabi_memset.o)
    0x08000278   0x0000004e   Code   RO         3822    .text               c_w.l(rt_memclr_w.o)
    0x080002c6   0x00000006   Code   RO         3824    .text               c_w.l(heapauxi.o)
    0x080002cc   0x000000ee   Code   RO         3883    .text               c_w.l(lludivv7m.o)
    0x080003ba   0x00000016   Code   RO         3885    .text               c_w.l(_rserrno.o)
    0x080003d0   0x00000044   Code   RO         3887    .text               c_w.l(rt_memclr.o)
    0x08000414   0x00000008   Code   RO         3975    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x0800041c   0x00000008   Code   RO         3989    .text               c_w.l(libspace.o)
    0x08000424   0x0000004a   Code   RO         3992    .text               c_w.l(sys_stackheap_outer.o)
    0x0800046e   0x00000012   Code   RO         3996    .text               c_w.l(exit.o)
    0x08000480   0x0000000c   Code   RO         4074    .text               c_w.l(sys_exit.o)
    0x0800048c   0x00000002   Code   RO         4091    .text               c_w.l(use_no_semi.o)
    0x0800048e   0x00000000   Code   RO         4093    .text               c_w.l(indicate_semi.o)
    0x0800048e   0x00000002   Code   RO          187    i.BusFault_Handler  stm32f1xx_it.o
    0x08000490   0x000000b8   Code   RO         3333    i.ControlYawMovement  Direction.o
    0x08000548   0x00000002   Code   RO          188    i.DebugMon_Handler  stm32f1xx_it.o
    0x0800054a   0x00000004   Code   RO         3660    i.Error_Handler     main.o
    0x0800054e   0x00000002   PAD
    0x08000550   0x00000038   Code   RO         3533    i.Find_Line_Begins  fine_line.o
    0x08000588   0x00000024   Code   RO          380    i.HAL_Delay         stm32f1xx_hal.o
    0x080005ac   0x000001e0   Code   RO         1022    i.HAL_GPIO_Init     stm32f1xx_hal_gpio.o
    0x0800078c   0x0000000a   Code   RO         1024    i.HAL_GPIO_ReadPin  stm32f1xx_hal_gpio.o
    0x08000796   0x00000010   Code   RO         1025    i.HAL_GPIO_TogglePin  stm32f1xx_hal_gpio.o
    0x080007a6   0x0000000a   Code   RO         1026    i.HAL_GPIO_WritePin  stm32f1xx_hal_gpio.o
    0x080007b0   0x0000000c   Code   RO          384    i.HAL_GetTick       stm32f1xx_hal.o
    0x080007bc   0x00000188   Code   RO         1132    i.HAL_I2C_Init      stm32f1xx_hal_i2c.o
    0x08000944   0x00000250   Code   RO         1150    i.HAL_I2C_Mem_Read  stm32f1xx_hal_i2c.o
    0x08000b94   0x00000130   Code   RO         1153    i.HAL_I2C_Mem_Write  stm32f1xx_hal_i2c.o
    0x08000cc4   0x00000060   Code   RO          122    i.HAL_I2C_MspInit   i2c.o
    0x08000d24   0x00000010   Code   RO          390    i.HAL_IncTick       stm32f1xx_hal.o
    0x08000d34   0x00000024   Code   RO          391    i.HAL_Init          stm32f1xx_hal.o
    0x08000d58   0x00000040   Code   RO          392    i.HAL_InitTick      stm32f1xx_hal.o
    0x08000d98   0x0000003c   Code   RO          163    i.HAL_MspInit       stm32f1xx_hal_msp.o
    0x08000dd4   0x0000001a   Code   RO          549    i.HAL_NVIC_EnableIRQ  stm32f1xx_hal_cortex.o
    0x08000dee   0x00000002   PAD
    0x08000df0   0x00000040   Code   RO          555    i.HAL_NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08000e30   0x00000024   Code   RO          556    i.HAL_NVIC_SetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x08000e54   0x0000012c   Code   RO         1684    i.HAL_RCC_ClockConfig  stm32f1xx_hal_rcc.o
    0x08000f80   0x00000020   Code   RO         1691    i.HAL_RCC_GetPCLK1Freq  stm32f1xx_hal_rcc.o
    0x08000fa0   0x0000004c   Code   RO         1693    i.HAL_RCC_GetSysClockFreq  stm32f1xx_hal_rcc.o
    0x08000fec   0x00000320   Code   RO         1696    i.HAL_RCC_OscConfig  stm32f1xx_hal_rcc.o
    0x0800130c   0x00000028   Code   RO          560    i.HAL_SYSTICK_Config  stm32f1xx_hal_cortex.o
    0x08001334   0x00000002   Code   RO         2529    i.HAL_TIMEx_BreakCallback  stm32f1xx_hal_tim_ex.o
    0x08001336   0x00000002   Code   RO         2530    i.HAL_TIMEx_CommutCallback  stm32f1xx_hal_tim_ex.o
    0x08001338   0x00000068   Code   RO         2548    i.HAL_TIMEx_MasterConfigSynchronization  stm32f1xx_hal_tim_ex.o
    0x080013a0   0x0000005a   Code   RO         1825    i.HAL_TIM_Base_Init  stm32f1xx_hal_tim.o
    0x080013fa   0x00000002   PAD
    0x080013fc   0x00000038   Code   RO          308    i.HAL_TIM_Base_MspInit  tim.o
    0x08001434   0x00000058   Code   RO         1830    i.HAL_TIM_Base_Start_IT  stm32f1xx_hal_tim.o
    0x0800148c   0x000000dc   Code   RO         1834    i.HAL_TIM_ConfigClockSource  stm32f1xx_hal_tim.o
    0x08001568   0x000000a4   Code   RO         1846    i.HAL_TIM_Encoder_Init  stm32f1xx_hal_tim.o
    0x0800160c   0x00000054   Code   RO          310    i.HAL_TIM_Encoder_MspInit  tim.o
    0x08001660   0x0000008e   Code   RO         1849    i.HAL_TIM_Encoder_Start  stm32f1xx_hal_tim.o
    0x080016ee   0x00000002   Code   RO         1859    i.HAL_TIM_IC_CaptureCallback  stm32f1xx_hal_tim.o
    0x080016f0   0x00000166   Code   RO         1873    i.HAL_TIM_IRQHandler  stm32f1xx_hal_tim.o
    0x08001856   0x00000002   PAD
    0x08001858   0x00000048   Code   RO          311    i.HAL_TIM_MspPostInit  tim.o
    0x080018a0   0x00000002   Code   RO         1876    i.HAL_TIM_OC_DelayElapsedCallback  stm32f1xx_hal_tim.o
    0x080018a2   0x000000cc   Code   RO         1897    i.HAL_TIM_PWM_ConfigChannel  stm32f1xx_hal_tim.o
    0x0800196e   0x0000005a   Code   RO         1900    i.HAL_TIM_PWM_Init  stm32f1xx_hal_tim.o
    0x080019c8   0x00000002   Code   RO         1902    i.HAL_TIM_PWM_MspInit  stm32f1xx_hal_tim.o
    0x080019ca   0x00000002   Code   RO         1903    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f1xx_hal_tim.o
    0x080019cc   0x0000009c   Code   RO         1905    i.HAL_TIM_PWM_Start  stm32f1xx_hal_tim.o
    0x08001a68   0x00000002   Code   RO         1911    i.HAL_TIM_PeriodElapsedCallback  stm32f1xx_hal_tim.o
    0x08001a6a   0x00000002   Code   RO         1916    i.HAL_TIM_TriggerCallback  stm32f1xx_hal_tim.o
    0x08001a6c   0x00000002   Code   RO          189    i.HardFault_Handler  stm32f1xx_it.o
    0x08001a6e   0x0000002e   Code   RO         1175    i.I2C_IsAcknowledgeFailed  stm32f1xx_hal_i2c.o
    0x08001a9c   0x000000fc   Code   RO         1185    i.I2C_RequestMemoryRead  stm32f1xx_hal_i2c.o
    0x08001b98   0x000000a8   Code   RO         1186    i.I2C_RequestMemoryWrite  stm32f1xx_hal_i2c.o
    0x08001c40   0x00000056   Code   RO         1190    i.I2C_WaitOnBTFFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x08001c96   0x00000002   PAD
    0x08001c98   0x00000090   Code   RO         1191    i.I2C_WaitOnFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x08001d28   0x000000bc   Code   RO         1192    i.I2C_WaitOnMasterAddressFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x08001de4   0x00000070   Code   RO         1193    i.I2C_WaitOnRXNEFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x08001e54   0x00000056   Code   RO         1194    i.I2C_WaitOnTXEFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x08001eaa   0x00000002   PAD
    0x08001eac   0x0000000c   Code   RO         3580    i.KEY_GetTaskIndex  key.o
    0x08001eb8   0x0000003c   Code   RO         3581    i.KEY_Init          key.o
    0x08001ef4   0x0000000c   Code   RO         3582    i.KEY_IsSelectionMode  key.o
    0x08001f00   0x0000000c   Code   RO         3583    i.KEY_IsTaskExecuted  key.o
    0x08001f0c   0x00000074   Code   RO         3584    i.KEY_Scan          key.o
    0x08001f80   0x00000010   Code   RO         3585    i.KEY_SetTaskExecuted  key.o
    0x08001f90   0x00000028   Code   RO         3586    i.KEY_SwitchTask    key.o
    0x08001fb8   0x0000016c   Code   RO         3587    i.KEY_UpdateOLED    key.o
    0x08002124   0x00000034   Code   RO         3588    i.KEY_UpdateSelectionTimer  key.o
    0x08002158   0x000001d0   Code   RO         2793    i.MPU6050_DMP_Get_Data  MPU6050.o
    0x08002328   0x000000b0   Code   RO         2794    i.MPU6050_DMP_Init  MPU6050.o
    0x080023d8   0x000000e4   Code   RO            4    i.MX_GPIO_Init      gpio.o
    0x080024bc   0x00000040   Code   RO          123    i.MX_I2C2_Init      i2c.o
    0x080024fc   0x000000a8   Code   RO          312    i.MX_TIM2_Init      tim.o
    0x080025a4   0x0000006c   Code   RO          313    i.MX_TIM4_Init      tim.o
    0x08002610   0x00000002   Code   RO          190    i.MemManage_Handler  stm32f1xx_it.o
    0x08002612   0x00000002   Code   RO          191    i.NMI_Handler       stm32f1xx_it.o
    0x08002614   0x00000026   Code   RO         3410    i.OLED_Clear        OLED.o
    0x0800263a   0x00000016   Code   RO         3411    i.OLED_I2C_Init     OLED.o
    0x08002650   0x00000036   Code   RO         3412    i.OLED_I2C_SendByte  OLED.o
    0x08002686   0x0000001e   Code   RO         3413    i.OLED_I2C_Start    OLED.o
    0x080026a4   0x00000018   Code   RO         3414    i.OLED_I2C_Stop     OLED.o
    0x080026bc   0x000000ac   Code   RO         3415    i.OLED_Init         OLED.o
    0x08002768   0x0000000e   Code   RO         3416    i.OLED_Pow          OLED.o
    0x08002776   0x00000022   Code   RO         3417    i.OLED_SetCursor    OLED.o
    0x08002798   0x00000068   Code   RO         3419    i.OLED_ShowChar     OLED.o
    0x08002800   0x00000042   Code   RO         3421    i.OLED_ShowNum      OLED.o
    0x08002842   0x00000058   Code   RO         3422    i.OLED_ShowSignedNum  OLED.o
    0x0800289a   0x00000026   Code   RO         3423    i.OLED_ShowString   OLED.o
    0x080028c0   0x00000010   Code   RO         3424    i.OLED_W_SCL        OLED.o
    0x080028d0   0x00000010   Code   RO         3425    i.OLED_W_SDA        OLED.o
    0x080028e0   0x00000022   Code   RO         3426    i.OLED_WriteCommand  OLED.o
    0x08002902   0x00000022   Code   RO         3427    i.OLED_WriteData    OLED.o
    0x08002924   0x0000003c   Code   RO         3720    i.PID_init          pid.o
    0x08002960   0x00000052   Code   RO         3721    i.PID_realize       pid.o
    0x080029b2   0x00000002   Code   RO          192    i.PendSV_Handler    stm32f1xx_it.o
    0x080029b4   0x00000002   Code   RO          193    i.SVC_Handler       stm32f1xx_it.o
    0x080029b6   0x00000002   PAD
    0x080029b8   0x00000040   Code   RO         3375    i.SetSpeedL         Motor.o
    0x080029f8   0x00000040   Code   RO         3376    i.SetSpeedR         Motor.o
    0x08002a38   0x00000004   Code   RO          194    i.SysTick_Handler   stm32f1xx_it.o
    0x08002a3c   0x0000005e   Code   RO         3661    i.SystemClock_Config  main.o
    0x08002a9a   0x00000002   Code   RO          270    i.SystemInit        system_stm32f1xx.o
    0x08002a9c   0x0000000c   Code   RO          195    i.TIM2_IRQHandler   stm32f1xx_it.o
    0x08002aa8   0x0000006c   Code   RO         1918    i.TIM_Base_SetConfig  stm32f1xx_hal_tim.o
    0x08002b14   0x0000001a   Code   RO         1919    i.TIM_CCxChannelCmd  stm32f1xx_hal_tim.o
    0x08002b2e   0x00000014   Code   RO         1929    i.TIM_ETR_SetConfig  stm32f1xx_hal_tim.o
    0x08002b42   0x00000010   Code   RO         1930    i.TIM_ITRx_SetConfig  stm32f1xx_hal_tim.o
    0x08002b52   0x00000002   PAD
    0x08002b54   0x00000050   Code   RO         1931    i.TIM_OC1_SetConfig  stm32f1xx_hal_tim.o
    0x08002ba4   0x00000058   Code   RO         1932    i.TIM_OC2_SetConfig  stm32f1xx_hal_tim.o
    0x08002bfc   0x00000058   Code   RO         1933    i.TIM_OC3_SetConfig  stm32f1xx_hal_tim.o
    0x08002c54   0x00000044   Code   RO         1934    i.TIM_OC4_SetConfig  stm32f1xx_hal_tim.o
    0x08002c98   0x00000022   Code   RO         1936    i.TIM_TI1_ConfigInputStage  stm32f1xx_hal_tim.o
    0x08002cba   0x00000024   Code   RO         1938    i.TIM_TI2_ConfigInputStage  stm32f1xx_hal_tim.o
    0x08002cde   0x00000002   Code   RO          196    i.UsageFault_Handler  stm32f1xx_it.o
    0x08002ce0   0x00000028   Code   RO         3940    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x08002d08   0x00000020   Code   RO          562    i.__NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08002d28   0x000000aa   Code   RO         3942    i.__kernel_poly     m_ws.l(poly.o)
    0x08002dd2   0x00000006   Code   RO         3927    i.__mathlib_dbl_infnan  m_ws.l(dunder.o)
    0x08002dd8   0x00000004   Code   RO         3928    i.__mathlib_dbl_infnan2  m_ws.l(dunder.o)
    0x08002ddc   0x0000000c   Code   RO         3929    i.__mathlib_dbl_invalid  m_ws.l(dunder.o)
    0x08002de8   0x00000010   Code   RO         3932    i.__mathlib_dbl_underflow  m_ws.l(dunder.o)
    0x08002df8   0x00000008   Code   RO         3377    i.abds              Motor.o
    0x08002e00   0x00000080   Code   RO         2836    i.accel_self_test   inv_mpu.o
    0x08002e80   0x00000270   Code   RO         3865    i.asin              m_ws.l(asin.o)
    0x080030f0   0x00000220   Code   RO         3917    i.atan              m_ws.l(atan.o)
    0x08003310   0x0000019c   Code   RO         3875    i.atan2             m_ws.l(atan2.o)
    0x080034ac   0x0000003a   Code   RO         3148    i.dmp_enable_6x_lp_quat  inv_mpu_dmp_motion_driver.o
    0x080034e6   0x00000002   PAD
    0x080034e8   0x000001d8   Code   RO         3149    i.dmp_enable_feature  inv_mpu_dmp_motion_driver.o
    0x080036c0   0x0000003c   Code   RO         3150    i.dmp_enable_gyro_cal  inv_mpu_dmp_motion_driver.o
    0x080036fc   0x0000003a   Code   RO         3151    i.dmp_enable_lp_quat  inv_mpu_dmp_motion_driver.o
    0x08003736   0x00000002   PAD
    0x08003738   0x00000014   Code   RO         3156    i.dmp_load_motion_driver_firmware  inv_mpu_dmp_motion_driver.o
    0x0800374c   0x00000168   Code   RO         3157    i.dmp_read_fifo     inv_mpu_dmp_motion_driver.o
    0x080038b4   0x000000d8   Code   RO         3160    i.dmp_set_accel_bias  inv_mpu_dmp_motion_driver.o
    0x0800398c   0x00000064   Code   RO         3161    i.dmp_set_fifo_rate  inv_mpu_dmp_motion_driver.o
    0x080039f0   0x000000cc   Code   RO         3162    i.dmp_set_gyro_bias  inv_mpu_dmp_motion_driver.o
    0x08003abc   0x0000010c   Code   RO         3164    i.dmp_set_orientation  inv_mpu_dmp_motion_driver.o
    0x08003bc8   0x00000030   Code   RO         3167    i.dmp_set_shake_reject_thresh  inv_mpu_dmp_motion_driver.o
    0x08003bf8   0x00000020   Code   RO         3168    i.dmp_set_shake_reject_time  inv_mpu_dmp_motion_driver.o
    0x08003c18   0x00000020   Code   RO         3169    i.dmp_set_shake_reject_timeout  inv_mpu_dmp_motion_driver.o
    0x08003c38   0x00000040   Code   RO         3170    i.dmp_set_tap_axes  inv_mpu_dmp_motion_driver.o
    0x08003c78   0x00000022   Code   RO         3171    i.dmp_set_tap_count  inv_mpu_dmp_motion_driver.o
    0x08003c9a   0x00000002   PAD
    0x08003c9c   0x00000128   Code   RO         3172    i.dmp_set_tap_thresh  inv_mpu_dmp_motion_driver.o
    0x08003dc4   0x00000020   Code   RO         3173    i.dmp_set_tap_time  inv_mpu_dmp_motion_driver.o
    0x08003de4   0x00000020   Code   RO         3174    i.dmp_set_tap_time_multi  inv_mpu_dmp_motion_driver.o
    0x08003e04   0x00000074   Code   RO         3534    i.get_LedFind_Scan  fine_line.o
    0x08003e78   0x000000c0   Code   RO         2837    i.get_accel_prod_shift  inv_mpu.o
    0x08003f38   0x0000043e   Code   RO         2838    i.get_st_biases     inv_mpu.o
    0x08004376   0x00000002   PAD
    0x08004378   0x000000e4   Code   RO         2839    i.gyro_self_test    inv_mpu.o
    0x0800445c   0x00000048   Code   RO         2795    i.inv_row_2_scale   MPU6050.o
    0x080044a4   0x00000028   Code   RO         3535    i.line_detect       fine_line.o
    0x080044cc   0x000000cc   Code   RO         3662    i.main              main.o
    0x08004598   0x00000054   Code   RO         2840    i.mpu_configure_fifo  inv_mpu.o
    0x080045ec   0x0000003c   Code   RO         2841    i.mpu_get_accel_fsr  inv_mpu.o
    0x08004628   0x00000044   Code   RO         2843    i.mpu_get_accel_sens  inv_mpu.o
    0x0800466c   0x00000034   Code   RO         2849    i.mpu_get_gyro_fsr  inv_mpu.o
    0x080046a0   0x00000040   Code   RO         2851    i.mpu_get_gyro_sens  inv_mpu.o
    0x080046e0   0x00000038   Code   RO         2853    i.mpu_get_lpf       inv_mpu.o
    0x08004718   0x0000001c   Code   RO         2855    i.mpu_get_sample_rate  inv_mpu.o
    0x08004734   0x00000158   Code   RO         2857    i.mpu_init          inv_mpu.o
    0x0800488c   0x000000bc   Code   RO         2858    i.mpu_load_firmware  inv_mpu.o
    0x08004948   0x000000e4   Code   RO         2859    i.mpu_lp_accel_mode  inv_mpu.o
    0x08004a2c   0x000000c4   Code   RO         2862    i.mpu_read_fifo_stream  inv_mpu.o
    0x08004af0   0x00000074   Code   RO         2863    i.mpu_read_mem      inv_mpu.o
    0x08004b64   0x000001d0   Code   RO         2866    i.mpu_reset_fifo    inv_mpu.o
    0x08004d34   0x000000f0   Code   RO         2867    i.mpu_run_self_test  inv_mpu.o
    0x08004e24   0x0000007c   Code   RO         2869    i.mpu_set_accel_fsr  inv_mpu.o
    0x08004ea0   0x00000148   Code   RO         2870    i.mpu_set_bypass    inv_mpu.o
    0x08004fe8   0x00000094   Code   RO         2872    i.mpu_set_dmp_state  inv_mpu.o
    0x0800507c   0x00000084   Code   RO         2873    i.mpu_set_gyro_fsr  inv_mpu.o
    0x08005100   0x00000078   Code   RO         2874    i.mpu_set_int_latched  inv_mpu.o
    0x08005178   0x0000007c   Code   RO         2876    i.mpu_set_lpf       inv_mpu.o
    0x080051f4   0x00000098   Code   RO         2877    i.mpu_set_sample_rate  inv_mpu.o
    0x0800528c   0x000000e0   Code   RO         2878    i.mpu_set_sensors   inv_mpu.o
    0x0800536c   0x00000074   Code   RO         2879    i.mpu_write_mem     inv_mpu.o
    0x080053e0   0x00000082   Code   RO         2796    i.run_self_test     MPU6050.o
    0x08005462   0x00000002   PAD
    0x08005464   0x0000006c   Code   RO         2880    i.set_int_enable    inv_mpu.o
    0x080054d0   0x0000004c   Code   RO         3946    i.sqrt              m_ws.l(sqrt.o)
    0x0800551c   0x00000026   Code   RO         3756    i.task_0            task.o
    0x08005542   0x00000002   PAD
    0x08005544   0x0000015c   Code   RO         3757    i.task_1            task.o
    0x080056a0   0x00000384   Code   RO         3758    i.task_2            task.o
    0x08005a24   0x00000208   Code   RO         3759    i.task_3            task.o
    0x08005c2c   0x000001d8   Code   RO         3760    i.task_4            task.o
    0x08005e04   0x00000062   Code   RO         3828    x$fpl$d2f           fz_ws.l(d2f.o)
    0x08005e66   0x00000002   PAD
    0x08005e68   0x00000150   Code   RO         3889    x$fpl$dadd          fz_ws.l(daddsub_clz.o)
    0x08005fb8   0x00000010   Code   RO         3977    x$fpl$dcheck1       fz_ws.l(dcheck1.o)
    0x08005fc8   0x00000018   Code   RO         3979    x$fpl$dcmpinf       fz_ws.l(dcmpi.o)
    0x08005fe0   0x000002b0   Code   RO         3896    x$fpl$ddiv          fz_ws.l(ddiv.o)
    0x08006290   0x00000078   Code   RO         3899    x$fpl$deqf          fz_ws.l(deqf.o)
    0x08006308   0x00000154   Code   RO         3830    x$fpl$dmul          fz_ws.l(dmul.o)
    0x0800645c   0x0000009c   Code   RO         3901    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x080064f8   0x0000000c   Code   RO         3903    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x08006504   0x00000016   Code   RO         3890    x$fpl$drsb          fz_ws.l(daddsub_clz.o)
    0x0800651a   0x00000002   PAD
    0x0800651c   0x000001cc   Code   RO         3983    x$fpl$dsqrt         fz_ws.l(dsqrt_noumaal.o)
    0x080066e8   0x000001d4   Code   RO         3891    x$fpl$dsub          fz_ws.l(daddsub_clz.o)
    0x080068bc   0x00000056   Code   RO         3832    x$fpl$f2d           fz_ws.l(f2d.o)
    0x08006912   0x00000002   PAD
    0x08006914   0x000000c4   Code   RO         3834    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x080069d8   0x0000000c   Code   RO         3905    x$fpl$fcheck1       fz_ws.l(fcheck1.o)
    0x080069e4   0x00000018   Code   RO         3985    x$fpl$fcmpinf       fz_ws.l(fcmpi.o)
    0x080069fc   0x00000184   Code   RO         3841    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x08006b80   0x00000036   Code   RO         3844    x$fpl$ffix          fz_ws.l(ffix.o)
    0x08006bb6   0x00000002   PAD
    0x08006bb8   0x0000003e   Code   RO         3848    x$fpl$ffixu         fz_ws.l(ffixu.o)
    0x08006bf6   0x00000002   PAD
    0x08006bf8   0x00000030   Code   RO         3853    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x08006c28   0x00000026   Code   RO         3852    x$fpl$ffltu         fz_ws.l(fflt_clz.o)
    0x08006c4e   0x00000002   PAD
    0x08006c50   0x00000068   Code   RO         3907    x$fpl$fleqf         fz_ws.l(fleqf.o)
    0x08006cb8   0x00000102   Code   RO         3858    x$fpl$fmul          fz_ws.l(fmul.o)
    0x08006dba   0x0000008c   Code   RO         3909    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x08006e46   0x0000000a   Code   RO         3911    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x08006e50   0x00000062   Code   RO         3860    x$fpl$frleqf        fz_ws.l(frleqf.o)
    0x08006eb2   0x00000002   PAD
    0x08006eb4   0x000000ea   Code   RO         3836    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x08006f9e   0x00000064   Code   RO         3987    x$fpl$retnan        fz_ws.l(retnan.o)
    0x08007002   0x0000005c   Code   RO         3913    x$fpl$scalbn        fz_ws.l(scalbn.o)
    0x0800705e   0x0000004c   Code   RO         3862    x$fpl$scalbnf       fz_ws.l(scalbnf.o)
    0x080070aa   0x00000030   Code   RO         4004    x$fpl$trapveneer    fz_ws.l(trapv.o)
    0x080070da   0x00000000   Code   RO         3915    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x080070da   0x00000010   Data   RO          271    .constdata          system_stm32f1xx.o
    0x080070ea   0x00000008   Data   RO          272    .constdata          system_stm32f1xx.o
    0x080070f2   0x00000012   Data   RO         1697    .constdata          stm32f1xx_hal_rcc.o
    0x08007104   0x0000001e   Data   RO         2881    .constdata          inv_mpu.o
    0x08007122   0x0000000c   Data   RO         2882    .constdata          inv_mpu.o
    0x0800712e   0x00000002   PAD
    0x08007130   0x00000028   Data   RO         2883    .constdata          inv_mpu.o
    0x08007158   0x00000bf6   Data   RO         3176    .constdata          inv_mpu_dmp_motion_driver.o
    0x08007d4e   0x000005f0   Data   RO         3428    .constdata          OLED.o
    0x0800833e   0x00000002   PAD
    0x08008340   0x00000050   Data   RO         3866    .constdata          m_ws.l(asin.o)
    0x08008390   0x00000098   Data   RO         3918    .constdata          m_ws.l(atan.o)
    0x08008428   0x00000008   Data   RO         3944    .constdata          m_ws.l(qnan.o)
    0x08008430   0x00000020   Data   RO         4136    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Base: 0x20000000, Size: 0x00000890, Max: 0x00005000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00000004   Data   RW          273    .data               system_stm32f1xx.o
    0x20000004   0x0000000c   Data   RW          398    .data               stm32f1xx_hal.o
    0x20000010   0x00000009   Data   RW         2797    .data               MPU6050.o
    0x20000019   0x00000003   PAD
    0x2000001c   0x0000002c   Data   RW         2884    .data               inv_mpu.o
    0x20000048   0x00000064   Data   RW         3537    .data               fine_line.o
    0x200000ac   0x0000000c   Data   RW         3589    .data               key.o
    0x200000b8   0x00000004   Data   RW         3665    .data               main.o
    0x200000bc   0x00000004   Data   RW         3666    .data               main.o
    0x200000c0   0x00000028   Data   RW         3761    .data               task.o
    0x200000e8   0x00000054   Zero   RW          124    .bss                i2c.o
    0x2000013c   0x00000090   Zero   RW          314    .bss                tim.o
    0x200001cc   0x00000010   Zero   RW         3175    .bss                inv_mpu_dmp_motion_driver.o
    0x200001dc   0x00000014   Zero   RW         3536    .bss                fine_line.o
    0x200001f0   0x00000040   Zero   RW         3724    .bss                pid.o
    0x20000230   0x00000060   Zero   RW         3990    .bss                c_w.l(libspace.o)
    0x20000290   0x00000200   Zero   RW         3806    HEAP                startup_stm32f103xb.o
    0x20000490   0x00000400   Zero   RW         3805    STACK               startup_stm32f103xb.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       184         18          0          0          0       1229   Direction.o
       842          8          0          9          0       5306   MPU6050.o
       136         16          0          0          0       1676   Motor.o
       784         14       1520          0          0       9166   OLED.o
       212         22          0        100         20       2471   fine_line.o
       228         12          0          0          0     425711   gpio.o
       160         28          0          0         84       1569   i2c.o
      5398        280         82         44          0      26721   inv_mpu.o
      2386        118       3062          0         16      19370   inv_mpu_dmp_motion_driver.o
       684        228          0         12          0       5323   key.o
       302         22          0          8          0       2914   main.o
       142         14          0          0         64       1480   pid.o
        64         26        236          0       1536        764   startup_stm32f103xb.o
       164         28          0         12          0       5789   stm32f1xx_hal.o
       198         14          0          0          0      28851   stm32f1xx_hal_cortex.o
       516         34          0          0          0       4052   stm32f1xx_hal_gpio.o
      2370         56          0          0          0      12806   stm32f1xx_hal_i2c.o
        60          8          0          0          0        842   stm32f1xx_hal_msp.o
      1208         72         18          0          0       4481   stm32f1xx_hal_rcc.o
      2088         58          0          0          0      18093   stm32f1xx_hal_tim.o
       108         12          0          0          0       2345   stm32f1xx_hal_tim_ex.o
        32          6          0          0          0       4164   stm32f1xx_it.o
         2          0         24          4          0       1071   system_stm32f1xx.o
      2278        396          0         40          0       4309   task.o
       488         42          0          0        144       3515   tim.o

    ----------------------------------------------------------------------
     21062       <USER>       <GROUP>        232       1864     594018   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        28          0          4          3          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        22          0          0          0          0        100   _rserrno.o
        16          0          0          0          0         68   aeabi_memset.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        72          0          0          0          0         76   llsdiv.o
       238          0          0          0          0        100   lludivv7m.o
        88          0          0          0          0         76   memcmp.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        98          4          0          0          0         92   d2f.o
       826         16          0          0          0        348   daddsub_clz.o
        16          4          0          0          0         68   dcheck1.o
        24          0          0          0          0         68   dcmpi.o
       688        140          0          0          0        208   ddiv.o
       120          4          0          0          0         92   deqf.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
       460         56          0          0          0        120   dsqrt_noumaal.o
        86          4          0          0          0         84   f2d.o
       430          8          0          0          0        168   faddsub_clz.o
        12          4          0          0          0         68   fcheck1.o
        24          0          0          0          0         68   fcmpi.o
       388         76          0          0          0         96   fdiv.o
        54          4          0          0          0         84   ffix.o
        62          4          0          0          0         84   ffixu.o
        86          0          0          0          0        136   fflt_clz.o
       104          4          0          0          0         84   fleqf.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
        98          0          0          0          0         68   frleqf.o
       100          0          0          0          0         68   retnan.o
        92          0          0          0          0         68   scalbn.o
        76          0          0          0          0         68   scalbnf.o
        48          0          0          0          0         68   trapv.o
         0          0          0          0          0          0   usenofp.o
       624         52         80          0          0        168   asin.o
       544         70        152          0          0        124   atan.o
       412         38          0          0          0        144   atan2.o
        38          6          0          0          0        272   dunder.o
        40          0          0          0          0         68   fpclassify.o
       170          0          0          0          0         96   poly.o
         0          0          8          0          0          0   qnan.o
        76          0          0          0          0         84   sqrt.o

    ----------------------------------------------------------------------
      7592        <USER>        <GROUP>          0         96       4884   Library Totals
        18          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       862         20          0          0         96       1220   c_w.l
      4808        352          0          0          0       2708   fz_ws.l
      1904        166        240          0          0        956   m_ws.l

    ----------------------------------------------------------------------
      7592        <USER>        <GROUP>          0         96       4884   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     28654       2070       5218        232       1960     587718   Grand Totals
     28654       2070       5218        232       1960     587718   ELF Image Totals
     28654       2070       5218        232          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                33872 (  33.08kB)
    Total RW  Size (RW Data + ZI Data)              2192 (   2.14kB)
    Total ROM Size (Code + RO Data + RW Data)      34104 (  33.30kB)

==============================================================================

