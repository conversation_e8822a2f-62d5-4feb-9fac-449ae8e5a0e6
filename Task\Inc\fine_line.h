#ifndef __FINE_LINE_H
#define __FINE_LINE_H

#include "stm32f1xx_hal.h"

// 定义结构体类型
typedef struct {
    GPIO_TypeDef* GPIOx;
    uint16_t GPIO_Pin;
    uint8_t value;
} lineIDdef;

// 全局变量声明
extern char have_line;
extern lineIDdef linepin_list[8];

// 功能函数声明
void line_detect(void);                  // 读取传感器电平
uint8_t get_LedFind_Scan(void);         // 获取最先检测到黑线的位置
int Find_Line_Begins(void);             // 主循迹控制逻辑函数

#endif /* __FINE_LINE_H */
