#!/bin/bash
# STM32 Ninja - 烧录脚本
# 负责将程序烧录到MCU

# 加载通用函数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh" || exit 1

# 默认参数
PROJECT_DIR=""
PROGRAMMER=""
FILE_PATH=""

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --project)
            PROJECT_DIR="$2"
            shift 2
            ;;
        --programmer)
            PROGRAMMER="$2"
            shift 2
            ;;
        --file)
            FILE_PATH="$2"
            shift 2
            ;;
        *)
            error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 设置项目目录
if [ -z "$PROJECT_DIR" ]; then
    PROJECT_DIR="$(pwd)"
fi

cd "$PROJECT_DIR" || exit 1

# 查找要烧录的文件
find_flash_file() {
    if [ -n "$FILE_PATH" ] && [ -f "$FILE_PATH" ]; then
        echo "$FILE_PATH"
        return 0
    fi
    
    # 所有工程都在build目录下查找输出文件
    # 优先查找hex文件
    for file in build/*.hex; do
        if [ -f "$file" ]; then
            echo "$file"
            return 0
        fi
    done
    
    # 其次查找elf文件
    for file in build/*.elf; do
        if [ -f "$file" ]; then
            echo "$file"
            return 0
        fi
    done
    
    # 如果在build目录找不到，查找根目录
    for file in *.hex; do
        if [ -f "$file" ]; then
            echo "$file"
            return 0
        fi
    done
    
    for file in *.elf; do
        if [ -f "$file" ]; then
            echo "$file"
            return 0
        fi
    done
    
    return 1
}

# 主程序
main() {
    info "准备烧录..."
    
    # 查找文件
    local flash_file=$(find_flash_file)
    if [ -z "$flash_file" ]; then
        error "未找到可烧录的文件 (*.hex 或 *.elf)"
        error "请先编译项目"
        exit 1
    fi
    
    info "烧录文件: $flash_file"
    
    # 检测烧录器
    if [ -z "$PROGRAMMER" ]; then
        step "检测烧录器..."
        PROGRAMMER=$(detect_programmer)
        if [ -z "$PROGRAMMER" ]; then
            error "未检测到烧录器"
            echo ""
            echo "请检查："
            echo "  1. 烧录器是否正确连接"
            echo "  2. 驱动是否已安装"
            echo "  3. MCU是否已供电"
            exit 1
        fi
        success "检测到烧录器: $PROGRAMMER"
    fi
    
    # 查找OpenOCD
    local openocd=$(find_openocd)
    if [ -z "$openocd" ]; then
        error "未找到OpenOCD工具"
        exit 1
    fi
    
    # 根据配置或自动检测选择MCU配置文件
    local mcu_config=${MCU_CONFIG:-""}
    local mcu_device=""
    
    if [ -z "$mcu_config" ]; then
        # 优先使用编译时保存的MCU信息
        if [ -f "build/mcu_info.conf" ]; then
            info "使用编译时的MCU信息"
            source build/mcu_info.conf
            mcu_device="$MCU_DEVICE"
            
            # 根据MCU系列选择配置文件
            case "$MCU_SERIES" in
                STM32F0*)
                    mcu_config="stm32f0x.cfg"
                    ;;
                STM32F1*)
                    mcu_config="stm32f1x.cfg"
                    ;;
                STM32F2*)
                    mcu_config="stm32f2x.cfg"
                    ;;
                STM32F3*)
                    mcu_config="stm32f3x.cfg"
                    ;;
                STM32F4*)
                    mcu_config="stm32f4x.cfg"
                    ;;
                STM32F7*)
                    mcu_config="stm32f7x.cfg"
                    ;;
                STM32H7*)
                    mcu_config="stm32h7x.cfg"
                    ;;
                STM32L0*)
                    mcu_config="stm32l0.cfg"
                    ;;
                STM32L1*)
                    mcu_config="stm32l1.cfg"
                    ;;
                STM32L4*)
                    mcu_config="stm32l4x.cfg"
                    ;;
                STM32G0*)
                    mcu_config="stm32g0x.cfg"
                    ;;
                STM32G4*)
                    mcu_config="stm32g4x.cfg"
                    ;;
                *)
                    # 如果无法识别系列，尝试从设备名推断
                    case "$mcu_device" in
                        STM32F0*) mcu_config="stm32f0x.cfg" ;;
                        STM32F1*) mcu_config="stm32f1x.cfg" ;;
                        STM32F2*) mcu_config="stm32f2x.cfg" ;;
                        STM32F3*) mcu_config="stm32f3x.cfg" ;;
                        STM32F4*) mcu_config="stm32f4x.cfg" ;;
                        STM32F7*) mcu_config="stm32f7x.cfg" ;;
                        STM32H7*) mcu_config="stm32h7x.cfg" ;;
                        STM32L0*) mcu_config="stm32l0.cfg" ;;
                        STM32L1*) mcu_config="stm32l1.cfg" ;;
                        STM32L4*) mcu_config="stm32l4x.cfg" ;;
                        STM32G0*) mcu_config="stm32g0x.cfg" ;;
                        STM32G4*) mcu_config="stm32g4x.cfg" ;;
                        *) mcu_config="stm32f1x.cfg" ;;
                    esac
                    ;;
            esac
            
            info "检测到MCU: $mcu_device (系列: $MCU_SERIES)"
        # 如果没有编译信息，从配置文件读取默认MCU型号
        elif [ -f "config/ninja.conf" ] && grep -q "DEFAULT_MCU=" "config/ninja.conf"; then
            local default_mcu=$(grep "DEFAULT_MCU=" "config/ninja.conf" | cut -d'"' -f2)
            case "$default_mcu" in
                STM32F0*) mcu_config="stm32f0x.cfg" ;;
                STM32F1*) mcu_config="stm32f1x.cfg" ;;
                STM32F2*) mcu_config="stm32f2x.cfg" ;;
                STM32F3*) mcu_config="stm32f3x.cfg" ;;
                STM32F4*) mcu_config="stm32f4x.cfg" ;;
                STM32F7*) mcu_config="stm32f7x.cfg" ;;
                STM32H7*) mcu_config="stm32h7x.cfg" ;;
                STM32L0*) mcu_config="stm32l0.cfg" ;;
                STM32L1*) mcu_config="stm32l1.cfg" ;;
                STM32L4*) mcu_config="stm32l4x.cfg" ;;
                STM32G0*) mcu_config="stm32g0x.cfg" ;;
                STM32G4*) mcu_config="stm32g4x.cfg" ;;
                *) mcu_config="stm32f1x.cfg" ;;
            esac
            info "使用配置的MCU: $default_mcu"
        else
            # 默认使用F1系列配置
            mcu_config="stm32f1x.cfg"
            warn "未找到MCU信息，使用默认配置"
        fi
    fi
    
    info "使用MCU配置文件: $mcu_config"
    
    # 创建临时OpenOCD配置
    local temp_cfg=$(mktemp -t openocd_XXXXXX.cfg)
    
    # 从配置文件加载参数
    local speed=${PROGRAMMER_SPEED:-1000}
    local protocol=${TRANSPORT_PROTOCOL:-auto}
    local reset_cfg=${RESET_CONFIG:-srst_only}
    local verify=${VERIFY_FLASH:-true}
    local erase_mode=${ERASE_MODE:-auto}
    local debug_level=${DEBUG_LEVEL:-1}
    
    # 根据烧录器类型选择配置
    if [ "$PROGRAMMER" == "cmsis-dap" ]; then
        # CMSIS-DAP配置
        cat > "$temp_cfg" << EOF
# STM32 Ninja 烧录配置 - CMSIS-DAP
source [find interface/cmsis-dap.cfg]
$([ "$protocol" == "auto" ] && echo "transport select swd" || echo "transport select $protocol")
adapter speed $speed
source [find target/$mcu_config]
reset_config $reset_cfg

init
halt
$([ "$erase_mode" == "chip" ] && echo "flash erase_sector 0 0 last" || echo "")
flash write_image erase $(convert_path "$flash_file")
$([ "$verify" == "true" ] && echo "verify_image $(convert_path "$flash_file")" || echo "")
reset run
exit
EOF
    else
        # ST-Link或其他烧录器配置
        local transport_cmd="hla_swd"
        if [ "$protocol" == "swd" ]; then
            transport_cmd="hla_swd"
        elif [ "$protocol" == "jtag" ]; then
            transport_cmd="hla_jtag"
        fi
        
        cat > "$temp_cfg" << EOF
# STM32 Ninja 烧录配置
source [find interface/${PROGRAMMER}.cfg]
transport select $transport_cmd
adapter speed $speed
source [find target/$mcu_config]
reset_config $reset_cfg

init
halt
$([ "$erase_mode" == "chip" ] && echo "flash erase_sector 0 0 last" || echo "")
flash write_image erase $(convert_path "$flash_file")
$([ "$verify" == "true" ] && echo "verify_image $(convert_path "$flash_file")" || echo "")
reset run
exit
EOF
    fi
    
    # 执行烧录
    step "开始烧录..."
    local start_time=$(date +%s%3N)  # 毫秒级时间戳
    
    if $openocd -f "$temp_cfg" 2>&1 | while read -r line; do
        case "$line" in
            *Error*|*error*)
                echo -e "${RED}$line${NC}"
                ;;
            *Info*|*info*)
                echo -e "${BLUE}$line${NC}"
                ;;
            *wrote*|*Wrote*)
                echo -e "${GREEN}$line${NC}"
                ;;
            *)
                echo "$line"
                ;;
        esac
    done; then
        local end_time=$(date +%s%3N)
        local duration=$((end_time - start_time))
        
        # 转换为秒和毫秒
        local seconds=$((duration / 1000))
        local milliseconds=$((duration % 1000))
        
        success "烧录成功！"
        info "烧录时间: ${seconds}.${milliseconds}秒"
        rm -f "$temp_cfg"
        exit 0
    else
        error "烧录失败"
        rm -f "$temp_cfg"
        exit 1
    fi
}

# 执行主程序
main