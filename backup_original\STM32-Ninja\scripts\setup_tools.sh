#!/bin/bash
# STM32 Ninja - 工具路径设置

# 加载通用函数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh" || exit 1

# 配置文件路径
CONFIG_FILE="${NINJA_ROOT:-$(dirname "$SCRIPT_DIR")}/config/ninja.conf"

# 检测路径
detect_path() {
    local tool_name="$1"
    local test_file="$2"
    local common_paths=("${@:3}")
    
    echo "" >&2
    step "检测 $tool_name..." >&2
    
    for path in "${common_paths[@]}"; do
        if [ -f "$path/$test_file" ]; then
            success "找到: $path" >&2
            echo "$path"  # 返回找到的路径到stdout
            return 0
        fi
    done
    
    warn "未自动检测到 $tool_name" >&2
    return 1
}

# 主程序
main() {
    info "工具路径设置"
    echo ""
    
    # 加载现有配置
    if [ -f "$CONFIG_FILE" ]; then
        source "$CONFIG_FILE"
    fi
    
    # STM32CubeCLT路径
    echo "1. STM32CubeCLT 路径"
    echo "   当前设置: ${CUBECLT_PATH:-未设置}"
    
    local detected_path=$(detect_path "STM32CubeCLT" "GNU-tools-for-STM32/bin/arm-none-eabi-gcc.exe" \
        "/d/STM32CubeCLT_1.18.0" \
        "/d/STM32CubeCLT" \
        "/c/STM32CubeCLT_1.18.0" \
        "/c/STM32CubeCLT" \
        "/d/app/cubeclt/STM32CubeCLT_1.18.0" \
        "/c/Program Files/STMicroelectronics/STM32CubeCLT_1.18.0" \
        "/opt/st/stm32cubeclt")
    
    if [ -n "$detected_path" ]; then
        CUBECLT_PATH="$detected_path"
    else
        echo -n "   请输入STM32CubeCLT路径: "
        read new_path
        if [ -n "$new_path" ]; then
            # 转换Windows路径为Git Bash格式
            if [[ "$new_path" =~ ^[A-Za-z]: ]]; then
                # D:\path 或 D:/path -> /d/path
                drive_letter=$(echo "${new_path:0:1}" | tr '[:upper:]' '[:lower:]')
                # 提取驱动器后面的路径部分（去掉驱动器字母和冒号）
                path_part="${new_path:2}"
                # 将反斜杠替换为正斜杠
                path_part="${path_part//\\//}"
                # 组合成Git Bash格式路径
                CUBECLT_PATH="/${drive_letter}${path_part}"
                info "转换路径: $new_path -> $CUBECLT_PATH"
            else
                CUBECLT_PATH="$new_path"
            fi
        fi
    fi
    
    # MSYS2路径
    echo ""
    echo "2. MSYS2 路径"
    echo "   当前设置: ${MSYS2_PATH:-未设置}"
    
    local detected_msys2=$(detect_path "MSYS2" "mingw64/bin/openocd.exe" \
        "/d/app/msys2" \
        "/c/msys64" \
        "/c/tools/msys2")
    
    if [ -n "$detected_msys2" ]; then
        MSYS2_PATH="$detected_msys2"
    else
        echo -n "   请输入MSYS2路径 (可选): "
        read new_path
        if [ -n "$new_path" ]; then
            # 转换Windows路径为Git Bash格式
            if [[ "$new_path" =~ ^[A-Za-z]: ]]; then
                # D:\path 或 D:/path -> /d/path
                drive_letter=$(echo "${new_path:0:1}" | tr '[:upper:]' '[:lower:]')
                # 提取驱动器后面的路径部分（去掉驱动器字母和冒号）
                path_part="${new_path:2}"
                # 将反斜杠替换为正斜杠
                path_part="${path_part//\\//}"
                # 组合成Git Bash格式路径
                MSYS2_PATH="/${drive_letter}${path_part}"
                info "转换路径: $new_path -> $MSYS2_PATH"
            else
                MSYS2_PATH="$new_path"
            fi
        fi
    fi
    
    # 保存配置
    echo ""
    step "保存配置..."
    
    mkdir -p "$(dirname "$CONFIG_FILE")"
    cat > "$CONFIG_FILE" << EOF
# STM32 Ninja 配置文件
# 自动生成于 $(date)

# 工具路径配置
CUBECLT_PATH="$CUBECLT_PATH"
MSYS2_PATH="$MSYS2_PATH"

# 默认设置
DEFAULT_MCU="${DEFAULT_MCU:-STM32F103C8T6}"
DEFAULT_BUILD_TYPE="${DEFAULT_BUILD_TYPE:-Debug}"
DEFAULT_PROGRAMMER="${DEFAULT_PROGRAMMER:-stlink}"

# AI接口设置
API_ENABLED=${API_ENABLED:-true}
API_PORT=${API_PORT:-8080}
EOF
    
    success "配置已保存"
    
    # 验证工具链
    echo ""
    step "验证工具链..."
    if setup_toolchain; then
        success "工具链配置正确"
    else
        error "工具链配置有误，请检查路径设置"
    fi
}

# 执行主程序
main