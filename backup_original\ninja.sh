#!/bin/bash
# STM32 Ninja - 项目启动脚本

# 获取项目目录
PROJECT_DIR="$(dirname "$(readlink -f "$0" 2>/dev/null || realpath "$0" 2>/dev/null || echo "$0")")"

# 查找全局安装
if [ -z "$STM32_NINJA_HOME" ]; then
    # 尝试常见位置
    for path in "/d/STM32Ninja" "$HOME/.stm32ninja" "/opt/stm32ninja"; do
        if [ -f "$path/scripts/ninja_main.sh" ]; then
            export STM32_NINJA_HOME="$path"
            break
        fi
    done
fi

if [ -z "$STM32_NINJA_HOME" ] || [ ! -f "$STM32_NINJA_HOME/scripts/ninja_main.sh" ]; then
    echo "[错误] 未找到STM32 Ninja全局安装"
    echo "请先运行全局安装脚本"
    exit 1
fi

# 设置项目环境
export NINJA_PROJECT_DIR="$PROJECT_DIR"
cd "$PROJECT_DIR"

# 运行主程序
exec "$STM32_NINJA_HOME/scripts/ninja_main.sh" "$@"
