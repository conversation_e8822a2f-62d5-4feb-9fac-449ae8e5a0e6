[1/3] LIN<PERSON> "build/Ide To Keil.elf"
FAILED: build/Ide To Keil.elf 
arm-none-eabi-gcc -mcpu=cortex-m3 -mthumb -specs=nano.specs -specs=nosys.specs -Wl,--gc-sections -Wl,--print-memory-usage -TSTM32_FLASH.ld -o "build/Ide To Keil.elf" build/__Core_Src_gpio.o build/__Core_Src_i2c.o build/__Core_Src_stm32f1xx_hal_msp.o build/__Core_Src_stm32f1xx_it.o build/__Core_Src_system_stm32f1xx.o build/__Core_Src_tim.o build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal.o build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_cortex.o build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_dma.o build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_exti.o build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_flash.o build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_flash_ex.o build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_gpio.o build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_gpio_ex.o build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_i2c.o build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_pwr.o build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_rcc.o build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_rcc_ex.o build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_tim.o build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_tim_ex.o build/._Task_Mpu6050Dmp_inv_mpu.o build/._Task_Mpu6050Dmp_inv_mpu_dmp_motion_driver.o build/._Task_Mpu6050Dmp_MPU6050.o build/._Task_Src_Direction.o build/._Task_Src_fine_line.o build/._Task_Src_main.o build/._Task_Src_Motor.o build/._Task_Src_OLED.o build/._Task_Src_pid.o build/._Task_Src_task.o build/startup.o -lc -lm -lnosys
D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld.exe: D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp\libc_nano.a(libc_a-closer.o): in function `_close_r':
(.text._close_r+0xc): warning: _close is not implemented and will always fail
D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld.exe: D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp\libc_nano.a(libc_a-closer.o): note: the message above does not take linker garbage collection into account
D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld.exe: D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp\libc_nano.a(libc_a-lseekr.o): in function `_lseek_r':
(.text._lseek_r+0x10): warning: _lseek is not implemented and will always fail
D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld.exe: D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp\libc_nano.a(libc_a-lseekr.o): note: the message above does not take linker garbage collection into account
D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld.exe: D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp\libc_nano.a(libc_a-readr.o): in function `_read_r':
(.text._read_r+0x10): warning: _read is not implemented and will always fail
D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld.exe: D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp\libc_nano.a(libc_a-readr.o): note: the message above does not take linker garbage collection into account
D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld.exe: D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp\libc_nano.a(libc_a-writer.o): in function `_write_r':
(.text._write_r+0x10): warning: _write is not implemented and will always fail
D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld.exe: D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp\libc_nano.a(libc_a-writer.o): note: the message above does not take linker garbage collection into account
D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld.exe: build/._Task_Src_main.o: in function `main':
C:/Users/<USER>/Desktop/Ide To Keil/MDK-ARM/Task/Src/main.c:113:(.text.main+0x26): undefined reference to `KEY_Init'
D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld.exe: C:/Users/<USER>/Desktop/Ide To Keil/MDK-ARM/Task/Src/main.c:134:(.text.main+0x5e): undefined reference to `KEY_UpdateOLED'
D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld.exe: C:/Users/<USER>/Desktop/Ide To Keil/MDK-ARM/Task/Src/main.c:139:(.text.main+0x64): undefined reference to `KEY_Scan'
D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld.exe: C:/Users/<USER>/Desktop/Ide To Keil/MDK-ARM/Task/Src/main.c:144:(.text.main+0x72): undefined reference to `KEY_SwitchTask'
D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld.exe: C:/Users/<USER>/Desktop/Ide To Keil/MDK-ARM/Task/Src/main.c:148:(.text.main+0x76): undefined reference to `KEY_GetTaskIndex'

Memory region         Used Size  Region Size  %age Used

           FLASH:       45556 B       512 KB      8.69%

             RAM:        1928 B       128 KB      1.47%

collect2.exe: error: ld returned 1 exit status
ninja: build stopped: subcommand failed.
