#!/bin/bash
# STM32 Ninja - 智能Makefile转Ninja脚本
# 可以处理各种格式的Makefile

# 加载通用函数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh" || exit 1

# 解析Makefile中的wildcard函数
expand_wildcard() {
    local pattern="$1"
    # 移除$(wildcard )包装
    pattern=$(echo "$pattern" | sed 's/\$(wildcard\s*//' | sed 's/)$//')
    # 执行通配符展开
    eval "ls $pattern 2>/dev/null" || true
}

# 解析源文件列表
parse_sources() {
    local makefile="$1"
    local sources=""
    
    # 查找所有包含源文件的变量
    while IFS= read -r line; do
        if [[ "$line" =~ ^SOURCES.*= ]] || [[ "$line" =~ ^C_SOURCES.*= ]]; then
            # 提取赋值内容
            local value=$(echo "$line" | sed 's/^[^=]*=//')
            
            # 处理wildcard函数
            if [[ "$value" =~ \$\(wildcard ]]; then
                # 可能有多个wildcard
                while [[ "$value" =~ \$\(wildcard[^\)]*\) ]]; do
                    local wildcard_expr="${BASH_REMATCH[0]}"
                    local expanded=$(expand_wildcard "$wildcard_expr")
                    sources="$sources $expanded"
                    # 移除已处理的wildcard
                    value=${value//$wildcard_expr/}
                done
            else
                # 普通文件列表
                sources="$sources $value"
            fi
        fi
    done < "$makefile"
    
    echo "$sources"
}

# 生成build.ninja
generate_ninja() {
    info "智能分析Makefile并生成build.ninja..."
    
    # 提取项目名称
    local target=$(grep -m1 "^TARGET" Makefile 2>/dev/null | sed 's/TARGET\s*=\s*//' | tr -d ' ')
    target=${target:-project}
    
    info "项目名称: $target"
    
    # 创建build.ninja头部
    cat > build.ninja << EOF
# Ninja build file generated from Makefile
# Generated by STM32 Ninja (Smart Mode)
# Project: $target

# Variables
builddir = build

# 编译器设置
cc = arm-none-eabi-gcc
as = arm-none-eabi-gcc
ld = arm-none-eabi-gcc
objcopy = arm-none-eabi-objcopy
size = arm-none-eabi-size

EOF

    # 提取MCU设置
    local mcu_flags=""
    
    # 尝试从MCU变量获取
    local mcu=$(grep -m1 "^MCU" Makefile 2>/dev/null | sed 's/MCU\s*=\s*//')
    if [ -n "$mcu" ]; then
        mcu_flags="$mcu"
    else
        # 尝试从CPU变量获取
        local cpu=$(grep -m1 "^CPU" Makefile 2>/dev/null | sed 's/CPU\s*=\s*//')
        if [ -n "$cpu" ]; then
            mcu_flags="$cpu -mthumb"
        else
            # 默认值
            mcu_flags="-mcpu=cortex-m3 -mthumb"
        fi
    fi
    
    echo "# MCU设置" >> build.ninja
    echo "mcu_flags = $mcu_flags" >> build.ninja
    
    # 提取编译选项
    local cflags=$(grep -m1 "^CFLAGS" Makefile 2>/dev/null | sed 's/CFLAGS\s*=\s*//' | sed 's/\$(MCU)//')
    if [ -z "$cflags" ]; then
        cflags="-Wall -fdata-sections -ffunction-sections -g -O0"
    fi
    echo "cflags = \$mcu_flags $cflags" >> build.ninja
    echo "asflags = \$mcu_flags -x assembler-with-cpp" >> build.ninja
    
    # 提取链接选项
    local ldflags=$(grep -m1 "^LDFLAGS" Makefile 2>/dev/null | sed 's/LDFLAGS\s*=\s*//' | sed 's/\$(MCU)//')
    if [ -z "$ldflags" ]; then
        ldflags="-specs=nano.specs -Wl,--gc-sections"
    fi
    echo "ldflags = \$mcu_flags $ldflags" >> build.ninja
    
    # 提取包含路径
    echo "" >> build.ninja
    echo "# 包含路径" >> build.ninja
    local includes=$(grep "^INCLUDES" Makefile 2>/dev/null | sed 's/INCLUDES\s*=\s*//')
    echo "includes = $includes" >> build.ninja
    
    # 构建规则
    cat >> build.ninja << 'EOF'

# 构建规则
rule cc
  command = $cc $cflags $includes -MMD -MF $out.d -c $in -o $out
  description = CC $out
  depfile = $out.d
  deps = gcc

rule as
  command = $as $asflags -c $in -o $out
  description = AS $out

rule link
  command = $ld $ldflags -o $out $in -lc -lm -lnosys
  description = LINK $out

rule hex
  command = $objcopy -O ihex $in $out
  description = HEX $out

rule bin
  command = $objcopy -O binary -S $in $out
  description = BIN $out

rule size
  command = $size $in
  description = SIZE $out

# 源文件
EOF

    # 解析源文件
    info "扫描源文件..."
    local sources=$(parse_sources "Makefile")
    
    local all_objects=""
    local c_count=0
    local asm_count=0
    
    # 处理每个源文件
    for src in $sources; do
        if [ -f "$src" ]; then
            local obj="\$builddir/${src//\//_}"
            
            case "$src" in
                *.c)
                    obj="${obj%.c}.o"
                    echo "build $obj: cc $src" >> build.ninja
                    all_objects="$all_objects $obj"
                    ((c_count++))
                    ;;
                *.s|*.S)
                    obj="${obj%.s}.o"
                    obj="${obj%.S}.o"
                    echo "build $obj: as $src" >> build.ninja
                    all_objects="$all_objects $obj"
                    ((asm_count++))
                    ;;
            esac
        fi
    done
    
    info "找到 $c_count 个C文件, $asm_count 个汇编文件"
    
    # 链接规则
    echo "" >> build.ninja
    echo "# 链接" >> build.ninja
    echo "build \$builddir/$target.elf: link$all_objects" >> build.ninja
    echo "" >> build.ninja
    echo "# 生成hex和bin" >> build.ninja
    echo "build \$builddir/$target.hex: hex \$builddir/$target.elf" >> build.ninja
    echo "build \$builddir/$target.bin: bin \$builddir/$target.elf" >> build.ninja
    echo "" >> build.ninja
    echo "# 显示大小" >> build.ninja
    echo "build size: size \$builddir/$target.elf" >> build.ninja
    echo "" >> build.ninja
    echo "# 默认目标" >> build.ninja
    echo "default \$builddir/$target.elf \$builddir/$target.hex size" >> build.ninja
    echo "" >> build.ninja
    echo "# 清理" >> build.ninja
    echo "rule clean" >> build.ninja
    echo "  command = rm -rf \$builddir" >> build.ninja
    echo "  description = CLEAN" >> build.ninja
    echo "build clean: clean" >> build.ninja
    
    success "生成了 build.ninja"
}

# 主程序
main() {
    if [ ! -f "Makefile" ]; then
        error "未找到 Makefile"
        return 1
    fi
    
    generate_ninja
    return 0
}

# 执行主程序
main "$@"