#include "Direction.h"
#include "pid.h"
#include "Motor.h"
#include "MPU6050.h"
// 全局变量，用于存储左右电机的速度

extern tPid pidMPU6050YawMovement;//角度环用的PID
// 封装的控制函数
void ControlYawMovement(float base_speed, float pid_target, float pid_max_output) {
    float pitch, roll, yaw;
    short gx, gy, gz, ax, ay, az;
    float g_fMPU6050YawMovePidOut;

    // 初始化 PID 目标值
    pidMPU6050YawMovement.target_val = pid_target;

    // 循环等待 MPU6050 数据获取成功
    while (MPU6050_DMP_Get_Data(&pitch, &roll, &yaw, &gx, &gy, &gz, &ax, &ay, &az) != 0) {}

    // 通过 PID 控制计算目标速度
    g_fMPU6050YawMovePidOut = PID_realize(&pidMPU6050YawMovement, yaw);

    // 基础速度加减 PID 输出速度
    g_fMPU6050YawMovePidOut1 = base_speed + g_fMPU6050YawMovePidOut;
    g_fMPU6050YawMovePidOut2 = base_speed - g_fMPU6050YawMovePidOut;

    // 限幅处理
    if (g_fMPU6050YawMovePidOut1 > pid_max_output) g_fMPU6050YawMovePidOut1 = pid_max_output;
    if (g_fMPU6050YawMovePidOut1 < 0) g_fMPU6050YawMovePidOut1 =0;
    

    if (g_fMPU6050YawMovePidOut2 > pid_max_output) g_fMPU6050YawMovePidOut2 = pid_max_output;
    if (g_fMPU6050YawMovePidOut2 < 0) g_fMPU6050YawMovePidOut2 = 0;

    // 设置左右电机速度
    SetSpeedR(g_fMPU6050YawMovePidOut1);
    SetSpeedL(g_fMPU6050YawMovePidOut2);
}
