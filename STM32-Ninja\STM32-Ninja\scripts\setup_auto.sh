#!/bin/bash
# STM32 Ninja - 自动配置脚本
# 自动检测和配置工具链路径

# 加载通用函数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh" || exit 1

# 常见工具安装路径
COMMON_PATHS=(
    # STM32CubeCLT - 更多可能的路径
    "/d/STM32CubeCLT_1.18.0"
    "/d/STM32CubeCLT"
    "/c/STM32CubeCLT_1.18.0"
    "/c/STM32CubeCLT"
    "/d/app/cubeclt/STM32CubeCLT_1.18.0"
    "/c/Program Files/STMicroelectronics/STM32CubeCLT_1.18.0"
    "/c/Program Files/STMicroelectronics/STM32CubeCLT"
    "/c/ST/STM32CubeCLT_1.18.0"
    "/c/ST/STM32CubeCLT"
    "/opt/st/stm32cubeclt"
    
    # MSYS2
    "/d/app/msys2"
    "/c/msys64"
    "/c/tools/msys2"
    
    # Keil MDK
    "/c/Keil_v5"
    "/d/Keil_v5"
    "/c/Program Files/Keil_v5"
    "/d/Program Files/Keil_v5"
)

# 自动搜索工具
auto_search() {
    info "自动搜索工具链..."
    
    local found_cubeclt=""
    local found_msys2=""
    local found_keil=""
    
    # 搜索STM32CubeCLT
    step "搜索STM32CubeCLT..."
    for path in "${COMMON_PATHS[@]}"; do
        if [ -f "$path/GNU-tools-for-STM32/bin/arm-none-eabi-gcc.exe" ] || \
           [ -f "$path/GNU-tools-for-STM32/bin/arm-none-eabi-gcc" ]; then
            found_cubeclt="$path"
            success "找到STM32CubeCLT: $path"
            break
        fi
    done
    
    # 搜索MSYS2
    step "搜索MSYS2..."
    for path in "${COMMON_PATHS[@]}"; do
        if [ -f "$path/mingw64/bin/openocd.exe" ]; then
            found_msys2="$path"
            success "找到MSYS2: $path"
            break
        fi
    done
    
    # 搜索Keil
    step "搜索Keil MDK..."
    for path in "${COMMON_PATHS[@]}"; do
        if [ -f "$path/UV4/UV4.exe" ]; then
            found_keil="$path"
            success "找到Keil: $path"
            break
        fi
    done
    
    # 如果没找到，尝试更广泛的搜索
    if [ -z "$found_cubeclt" ]; then
        warn "未找到STM32CubeCLT，尝试搜索系统PATH..."
        if command -v arm-none-eabi-gcc &>/dev/null; then
            local gcc_path=$(which arm-none-eabi-gcc)
            found_cubeclt=$(dirname "$(dirname "$gcc_path")")
            success "在PATH中找到GCC工具链: $found_cubeclt"
        fi
    fi
    
    # 保存配置
    save_config "$found_cubeclt" "$found_msys2" "$found_keil"
}

# 保存配置
save_config() {
    local cubeclt="$1"
    local msys2="$2"
    local keil="$3"
    
    # 配置文件路径
    local config_file="${NINJA_ROOT:-$(dirname "$SCRIPT_DIR")}/config/ninja.conf"
    mkdir -p "$(dirname "$config_file")"
    
    # 写入配置
    cat > "$config_file" << EOF
# STM32 Ninja 配置文件
# 自动生成于 $(date)

# 工具路径配置
CUBECLT_PATH="${cubeclt:-/d/app/cubeclt/STM32CubeCLT_1.18.0}"
MSYS2_PATH="${msys2:-/d/app/msys2}"
KEIL_PATH="${keil:-/c/Keil_v5}"

# 默认设置
DEFAULT_MCU="STM32F103C8T6"
DEFAULT_BUILD_TYPE="Debug"
DEFAULT_PROGRAMMER="stlink"

# AI接口设置
API_ENABLED=true
API_PORT=8080

# 自动检测标记
AUTO_CONFIGURED=true
EOF
    
    success "配置已保存"
    
    # 显示结果
    echo ""
    echo "检测结果："
    echo "  STM32CubeCLT: ${cubeclt:-未找到}"
    echo "  MSYS2: ${msys2:-未找到}"
    echo "  Keil: ${keil:-未找到}"
    
    if [ -z "$cubeclt" ]; then
        echo ""
        warn "未找到STM32CubeCLT，请手动下载安装："
        echo "  https://www.st.com/en/development-tools/stm32cubeclt.html"
    fi
}

# 主程序
main() {
    info "STM32 Ninja 自动配置"
    echo ""
    
    # 检查是否已经自动配置过
    if [ -f "${NINJA_ROOT:-$(dirname "$SCRIPT_DIR")}/config/ninja.conf" ]; then
        source "${NINJA_ROOT:-$(dirname "$SCRIPT_DIR")}/config/ninja.conf"
        if [ "$AUTO_CONFIGURED" = "true" ]; then
            info "已经自动配置过"
            echo -n "是否重新配置? (y/N): "
            read -n 1 reconfigure
            echo ""
            if [ "$reconfigure" != "y" ] && [ "$reconfigure" != "Y" ]; then
                return 0
            fi
        fi
    fi
    
    # 执行自动搜索
    auto_search
    
    echo ""
    info "您可以随时运行 './scripts/setup_tools.sh' 手动调整配置"
}

# 执行主程序
main