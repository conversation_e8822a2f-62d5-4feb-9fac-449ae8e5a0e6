{"name": "Ide To Keil", "target": "Ide To Keil", "toolchain": "AC5", "toolchainLocation": "c:\\Keil_v5\\ARM\\ARMCC", "toolchainCfgFile": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.23.11\\res\\data\\models/arm.v5.model.json", "buildMode": "fast|multhread", "showRepathOnLog": true, "threadNum": 16, "rootDir": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "dumpPath": "build\\Ide To Keil", "outDir": "build\\Ide To Keil", "ram": 20480, "rom": 65536, "incDirs": ["../Core/Inc", "../Drivers/STM32F1xx_HAL_Driver/Inc", "../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy", "../Drivers/CMSIS/Device/ST/STM32F1xx/Include", "../Drivers/CMSIS/Include", "Task/Src", "Task/Inc", "Task/Mpu6050Dmp", ".cmsis/include", "RTE/_Ide To Keil"], "libDirs": [], "defines": ["USE_HAL_DRIVER", "STM32F103xB"], "sourceList": ["../Core/Src/gpio.c", "../Core/Src/i2c.c", "../Core/Src/stm32f1xx_hal_msp.c", "../Core/Src/stm32f1xx_it.c", "../Core/Src/system_stm32f1xx.c", "../Core/Src/tim.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_i2c.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim_ex.c", "Task/Mpu6050Dmp/MPU6050.c", "Task/Mpu6050Dmp/inv_mpu.c", "Task/Mpu6050Dmp/inv_mpu_dmp_motion_driver.c", "Task/Src/Direction.c", "Task/Src/Motor.c", "Task/Src/OLED.c", "Task/Src/fine_line.c", "Task/Src/key.c", "Task/Src/main.c", "Task/Src/pid.c", "Task/Src/task.c", "startup_stm32f103xb.s"], "alwaysInBuildSources": [], "sourceParams": {}, "options": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": false, "output-debug-info": "enable", "microcontroller-cpu": "cortex-m3", "microcontroller-fpu": "cortex-m3", "microcontroller-float": "cortex-m3", "$arch-extensions": "", "$clang-arch-extensions": "", "$armlink-arch-extensions": ""}, "c/cpp-compiler": {"optimization": "level-3", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "xo-base": "", "ro-base": "", "rw-base": "", "link-scatter": ["\"c:/Users/<USER>/Desktop/Ide To Keil/MDK-ARM/build/Ide To Keil/Ide To Keil.sct\""]}}, "env": {"KEIL_OUTPUT_DIR": "Ide To Keil", "workspaceFolder": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "workspaceFolderBasename": "MDK-ARM", "OutDir": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM\\build\\Ide To Keil", "OutDirRoot": "build", "OutDirBase": "build\\Ide To Keil", "ProjectName": "Ide To Keil", "ConfigName": "Ide To Keil", "ProjectRoot": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "ExecutableName": "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM\\build\\Ide To Keil\\Ide To Keil", "ChipPackDir": "", "ChipName": "", "SYS_Platform": "win32", "SYS_DirSep": "\\", "SYS_DirSeparator": "\\", "SYS_PathSep": ";", "SYS_PathSeparator": ";", "SYS_EOL": "\r\n", "EIDE_BUILDER_DIR": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.23.11\\res\\tools\\win32\\unify_builder", "EIDE_BINARIES_VER": "12.1.1", "EIDE_MSYS": "C:\\Users\\<USER>\\.eide\\bin\\builder\\msys\\bin", "ToolchainRoot": "c:\\Keil_v5\\ARM\\ARMCC"}, "sysPaths": []}