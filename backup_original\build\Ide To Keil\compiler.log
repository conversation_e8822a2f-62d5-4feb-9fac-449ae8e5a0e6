>>> cc

".\Task\Src\task.c", line 130: Warning:  #177-D: variable "case2_detect_count"  was declared but never referenced
  				static uint16_t case2_detect_count = 0;  // Case2检测计数器
  				                ^
".\Task\Src\task.c", line 225: Warning:  #177-D: variable "case4_detect_count"  was declared but never referenced
  				static uint16_t case4_detect_count = 0;  // Case4检测计数器
  				                ^
.\Task\Src\task.c: 2 warnings, 0 errors

>>> ld

Program Size: Code=28654 RO-data=5218 RW-data=232 ZI-data=1960  
