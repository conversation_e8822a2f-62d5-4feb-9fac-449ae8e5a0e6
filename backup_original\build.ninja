# Ninja build file for Ide To Keil
# Device: STM32F103C8
# Generated by STM32 Ninja Ultimate

builddir = build
target = Ide To Keil

# Toolchain
cc = arm-none-eabi-gcc
as = arm-none-eabi-gcc
ld = arm-none-eabi-gcc
objcopy = arm-none-eabi-objcopy
size = arm-none-eabi-size

# MCU flags
mcuflags = -mcpu=cortex-m3 -mthumb

# Compile flags
cflags = $mcuflags -Wall -fdata-sections -ffunction-sections -g -O0
asflags = $mcuflags -x assembler-with-cpp

# Defines
defines = -DSTM32F103C8 -DSTM32F103xB -DUSE_HAL_DRIVER

# Include paths (44 paths)
includes = -I. -I.. -I../.\Task\Inc -I../.\Task\Mpu6050Dmp -I../.\Task\Src -I../Core/Inc -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../MDK-ARM/.cmsis/device/ARM/ARMCA5/Config -I../MDK-ARM/.cmsis/device/ARM/ARMCA5/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCA7/Config -I../MDK-ARM/.cmsis/device/ARM/ARMCA7/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCA9/Config -I../MDK-ARM/.cmsis/device/ARM/ARMCA9/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM0/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM0plus/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM1/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM23/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM23/Include/Template -I../MDK-ARM/.cmsis/device/ARM/ARMCM3/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM33/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM33/Include/Template -I../MDK-ARM/.cmsis/device/ARM/ARMCM35P/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM35P/Include/Template -I../MDK-ARM/.cmsis/device/ARM/ARMCM4/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM55/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM55/Include/Template -I../MDK-ARM/.cmsis/device/ARM/ARMCM7/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM85/Include -I../MDK-ARM/.cmsis/device/ARM/ARMCM85/Include/Template -I../MDK-ARM/.cmsis/device/ARM/ARMSC000/Include -I../MDK-ARM/.cmsis/device/ARM/ARMSC300/Include -I../MDK-ARM/.cmsis/device/ARM/ARMv81MML/Include -I../MDK-ARM/.cmsis/device/ARM/ARMv81MML/Include/Template -I../MDK-ARM/.cmsis/device/ARM/ARMv8MBL/Include -I../MDK-ARM/.cmsis/device/ARM/ARMv8MBL/Include/Template -I../MDK-ARM/.cmsis/device/ARM/ARMv8MML/Include -I../MDK-ARM/.cmsis/device/ARM/ARMv8MML/Include/Template -I../MDK-ARM/.cmsis/include -I../MDK-ARM/RTE/_Ide_To_Keil -I../MDK-ARM/Task/Inc -I../MDK-ARM/Task/Mpu6050Dmp

# Link flags
ldflags = $mcuflags -specs=nano.specs -specs=nosys.specs
ldflags = $ldflags -Wl,--gc-sections -Wl,--print-memory-usage

# Build rules
rule cc
  command = $cc $cflags $includes $defines -MMD -MF $out.d -c $in -o $out
  description = CC $out
  depfile = $out.d
  deps = gcc

rule as
  command = $as $asflags -c $in -o $out
  description = AS $out

rule link
  command = $ld $ldflags -o $out $in -lc -lm -lnosys
  description = LINK $out

rule hex
  command = $objcopy -O ihex $in $out
  description = HEX $out

rule bin
  command = $objcopy -O binary -S $in $out
  description = BIN $out

# Source files
build $builddir/__Core_Src_gpio.o: cc ../Core/Src/gpio.c
build $builddir/__Core_Src_i2c.o: cc ../Core/Src/i2c.c
build $builddir/__Core_Src_stm32f1xx_hal_msp.o: cc ../Core/Src/stm32f1xx_hal_msp.c
build $builddir/__Core_Src_stm32f1xx_it.o: cc ../Core/Src/stm32f1xx_it.c
build $builddir/__Core_Src_system_stm32f1xx.o: cc ../Core/Src/system_stm32f1xx.c
build $builddir/__Core_Src_tim.o: cc ../Core/Src/tim.c
build $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal.o: cc ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c
build $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_cortex.o: cc ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c
build $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_dma.o: cc ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c
build $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_exti.o: cc ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c
build $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_flash.o: cc ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c
build $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_flash_ex.o: cc ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c
build $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_gpio.o: cc ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c
build $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_gpio_ex.o: cc ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c
build $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_i2c.o: cc ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_i2c.c
build $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_pwr.o: cc ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c
build $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_rcc.o: cc ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c
build $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_rcc_ex.o: cc ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c
build $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_tim.o: cc ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim.c
build $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_tim_ex.o: cc ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim_ex.c
build $builddir/._Task_Mpu6050Dmp_inv_mpu.o: cc ./Task/Mpu6050Dmp/inv_mpu.c
build $builddir/._Task_Mpu6050Dmp_inv_mpu_dmp_motion_driver.o: cc ./Task/Mpu6050Dmp/inv_mpu_dmp_motion_driver.c
build $builddir/._Task_Mpu6050Dmp_MPU6050.o: cc ./Task/Mpu6050Dmp/MPU6050.c
build $builddir/._Task_Src_Direction.o: cc ./Task/Src/Direction.c
build $builddir/._Task_Src_fine_line.o: cc ./Task/Src/fine_line.c
build $builddir/._Task_Src_key.o: cc ./Task/Src/key.c
build $builddir/._Task_Src_main.o: cc ./Task/Src/main.c
build $builddir/._Task_Src_Motor.o: cc ./Task/Src/Motor.c
build $builddir/._Task_Src_OLED.o: cc ./Task/Src/OLED.c
build $builddir/._Task_Src_pid.o: cc ./Task/Src/pid.c
build $builddir/._Task_Src_task.o: cc ./Task/Src/task.c

# Startup file
build $builddir/startup.o: as startup_gnu.s

# Linker script
ldflags = $ldflags -TSTM32_FLASH.ld

# Link
build $builddir/$target.elf: link $builddir/__Core_Src_gpio.o $builddir/__Core_Src_i2c.o $builddir/__Core_Src_stm32f1xx_hal_msp.o $builddir/__Core_Src_stm32f1xx_it.o $builddir/__Core_Src_system_stm32f1xx.o $builddir/__Core_Src_tim.o $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal.o $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_cortex.o $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_dma.o $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_exti.o $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_flash.o $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_flash_ex.o $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_gpio.o $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_gpio_ex.o $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_i2c.o $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_pwr.o $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_rcc.o $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_rcc_ex.o $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_tim.o $builddir/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_tim_ex.o $builddir/._Task_Mpu6050Dmp_inv_mpu.o $builddir/._Task_Mpu6050Dmp_inv_mpu_dmp_motion_driver.o $builddir/._Task_Mpu6050Dmp_MPU6050.o $builddir/._Task_Src_Direction.o $builddir/._Task_Src_fine_line.o $builddir/._Task_Src_key.o $builddir/._Task_Src_main.o $builddir/._Task_Src_Motor.o $builddir/._Task_Src_OLED.o $builddir/._Task_Src_pid.o $builddir/._Task_Src_task.o $builddir/startup.o

# Output files
build $builddir/$target.hex: hex $builddir/$target.elf
build $builddir/$target.bin: bin $builddir/$target.elf

rule size
  command = $size $in
  description = SIZE $in

build size: size $builddir/$target.elf
  implicit = $builddir/$target.hex

# Default target
default $builddir/$target.hex size

# Clean
rule clean
  command = rm -rf $builddir *.ld *.s
build clean: clean
