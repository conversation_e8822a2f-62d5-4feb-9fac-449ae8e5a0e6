
/*-Memory Regions-*/
define symbol __ICFEDIT_region_IROM1_start__ = 0x80000000;
define symbol __ICFEDIT_region_IROM1_end__   = 0x801FFFFF;
define symbol __ICFEDIT_region_IROM2_start__ = 0x0;
define symbol __ICFEDIT_region_IROM2_end__   = 0x0;
define symbol __ICFEDIT_region_EROM1_start__ = 0x0;
define symbol __ICFEDIT_region_EROM1_end__   = 0x0;
define symbol __ICFEDIT_region_EROM2_start__ = 0x0;
define symbol __ICFEDIT_region_EROM2_end__   = 0x0;
define symbol __ICFEDIT_region_EROM3_start__ = 0x0;
define symbol __ICFEDIT_region_EROM3_end__   = 0x0;
define symbol __ICFEDIT_region_IRAM1_start__ = 0x80200000;
define symbol __ICFEDIT_region_IRAM1_end__   = 0x803FFFFF;
define symbol __ICFEDIT_region_IRAM2_start__ = 0x0;
define symbol __ICFEDIT_region_IRAM2_end__   = 0x0;
define symbol __ICFEDIT_region_ERAM1_start__ = 0x0;
define symbol __ICFEDIT_region_ERAM1_end__   = 0x0;
define symbol __ICFEDIT_region_ERAM2_start__ = 0x0;
define symbol __ICFEDIT_region_ERAM2_end__   = 0x0;
define symbol __ICFEDIT_region_ERAM3_start__ = 0x0;
define symbol __ICFEDIT_region_ERAM3_end__   = 0x0;
define symbol __ICFEDIT_region_TTB_start__   = 0x80500000;
define symbol __ICFEDIT_region_TTB_end__     = 0x805FFFFF;

/*-Sizes-*/
define symbol __ICFEDIT_size_cstack__        = 0x1000;
define symbol __ICFEDIT_size_irqstack__      = 0x100;
define symbol __ICFEDIT_size_fiqstack__      = 0x100;
define symbol __ICFEDIT_size_svcstack__      = 0x100;
define symbol __ICFEDIT_size_abtstack__      = 0x100;
define symbol __ICFEDIT_size_undstack__      = 0x100;
define symbol __ICFEDIT_size_heap__          = 0x8000;
define symbol __ICFEDIT_size_ttb__           = 0x4000;

define memory mem with size = 4G;
define region IROM_region   =   mem:[from __ICFEDIT_region_IROM1_start__ to __ICFEDIT_region_IROM1_end__]
                              | mem:[from __ICFEDIT_region_IROM2_start__ to __ICFEDIT_region_IROM2_end__];
define region IRAM_region   =   mem:[from __ICFEDIT_region_IRAM1_start__ to __ICFEDIT_region_IRAM1_end__]
                              | mem:[from __ICFEDIT_region_IRAM2_start__ to __ICFEDIT_region_IRAM2_end__];
define region ERAM_region   =   mem:[from __ICFEDIT_region_ERAM1_start__ to __ICFEDIT_region_ERAM1_end__]
                              | mem:[from __ICFEDIT_region_ERAM2_start__ to __ICFEDIT_region_ERAM2_end__]
                              | mem:[from __ICFEDIT_region_ERAM3_start__ to __ICFEDIT_region_ERAM3_end__];
define region TTB_region    =   mem:[from __ICFEDIT_region_TTB_start__   to __ICFEDIT_region_TTB_end__  ];

define block USR_STACK     with alignment = 8, size = __ICFEDIT_size_cstack__        { };
define block IRQ_STACK     with alignment = 8, size = __ICFEDIT_size_irqstack__      { };
define block FIQ_STACK     with alignment = 8, size = __ICFEDIT_size_fiqstack__      { };
define block SVC_STACK     with alignment = 8, size = __ICFEDIT_size_svcstack__      { };
define block ABT_STACK     with alignment = 8, size = __ICFEDIT_size_abtstack__      { };
define block UND_STACK     with alignment = 8, size = __ICFEDIT_size_undstack__      { };
define block HEAP          with alignment = 8, size = __ICFEDIT_size_heap__          { };
define block TTB           with alignment = 8, size = __ICFEDIT_size_ttb__           { section TTB };

do not initialize  { section .noinit };

initialize by copy { readwrite };
if (isdefinedsymbol(__USE_DLIB_PERTHREAD))
{
  // Required in a multi-threaded application
  initialize by copy with packing = none { section __DLIB_PERTHREAD };
}

place at address mem:__ICFEDIT_region_IROM1_start__ { readonly section RESET };
place in IROM_region  { readonly };
place in IRAM_region  { readwrite, block HEAP, block USR_STACK, block IRQ_STACK, block FIQ_STACK, block SVC_STACK, block ABT_STACK, block UND_STACK };
place in TTB_region   { block TTB };