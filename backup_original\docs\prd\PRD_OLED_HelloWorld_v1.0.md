# 产品需求文档 (PRD) - OLED Hello World 项目重构

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-07-31
- **负责人**: Emma (产品经理)
- **项目名称**: STM32F103C8 OLED Hello World 显示项目
- **最后更新**: 2025-07-31

## 2. 背景与问题陈述

### 2.1 当前状况
当前STM32F103C8项目包含了多个复杂模块：
- MPU6050陀螺仪传感器模块 (Task/Mpu6050Dmp/)
- 电机控制模块 (Motor.c)
- PID控制算法 (pid.c)
- 方向控制模块 (Direction.c)
- 线检测模块 (fine_line.c)
- 按键控制模块 (key.c)
- 任务调度模块 (task.c)
- OLED显示模块 (OLED.c) ✓ 保留

### 2.2 问题陈述
老板要求简化项目，**只保留OLED显示功能**，并在屏幕上显示"Hello World"。当前项目过于复杂，包含了不必要的模块，需要进行大幅度精简。

### 2.3 解决目标
将复杂的多功能机器人控制系统简化为一个纯粹的OLED显示系统，实现最基本的"Hello World"显示功能。

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **简化项目结构**: 移除除OLED外的所有功能模块
2. **实现基本显示**: 在OLED屏幕上显示"Hello World"
3. **保持系统稳定**: 确保简化后的系统能够正常运行
4. **代码整洁**: 移除所有不必要的代码和依赖

### 3.2 关键结果 (Key Results)
- ✅ 成功移除MPU6050、电机、PID、按键等模块
- ✅ OLED正常初始化并显示内容
- ✅ "Hello World"文本在屏幕上清晰显示
- ✅ 系统启动后立即显示，无需用户交互
- ✅ 编译成功，无错误和警告
- ✅ 程序大小显著减小（预期减少70%以上）

### 3.3 反向指标 (Counter Metrics)
- ❌ 不应影响OLED的正常显示功能
- ❌ 不应引入新的编译错误
- ❌ 不应破坏STM32F103C8的基本HAL库功能

## 4. 用户画像与用户故事

### 4.1 目标用户
- **主要用户**: 嵌入式开发学习者
- **使用场景**: 学习STM32基础显示功能
- **技术水平**: 初级到中级嵌入式开发者

### 4.2 用户故事
**作为一个** 嵌入式开发学习者  
**我希望** 有一个简单的STM32 OLED显示示例  
**以便于** 我能够快速理解和学习OLED的基本使用方法  

**验收标准**:
- 系统上电后自动显示"Hello World"
- 显示内容清晰可读
- 代码结构简单易懂
- 无需复杂的用户交互

## 5. 功能规格详述

### 5.1 核心功能
1. **OLED初始化功能**
   - 保持现有的OLED_Init()函数
   - 确保I2C通信正常建立
   - 屏幕清空并准备显示

2. **Hello World显示功能**
   - 在屏幕第1行显示"Hello World"
   - 使用OLED_ShowString()函数
   - 字体清晰，位置居中

3. **系统基础功能**
   - 保持STM32F103C8的基本HAL库初始化
   - 保持系统时钟配置
   - 保持GPIO和I2C配置（OLED需要）

### 5.2 技术实现细节
```c
// 简化后的main函数逻辑
int main(void) {
    HAL_Init();
    SystemClock_Config();
    MX_GPIO_Init();
    MX_I2C2_Init();
    
    OLED_Init();
    OLED_Clear();
    OLED_ShowString(1, 1, "Hello World");
    
    while(1) {
        // 保持系统运行
        HAL_Delay(1000);
    }
}
```

### 5.3 边缘情况与异常处理
- **OLED初始化失败**: 系统继续运行，但无显示
- **I2C通信异常**: 重试机制或错误指示
- **电源不稳定**: 确保显示内容不闪烁

## 6. 范围定义

### 6.1 包含功能 (In Scope)
✅ **保留模块**:
- OLED显示模块 (OLED.c, OLED.h, OLED_Font.h)
- STM32F103C8 HAL库核心功能
- GPIO配置 (OLED I2C引脚)
- I2C配置 (OLED通信)
- 系统时钟配置
- 基本的main.c框架

✅ **新增功能**:
- 简化的Hello World显示逻辑

### 6.2 排除功能 (Out of Scope)
❌ **移除模块**:
- MPU6050陀螺仪模块 (整个Mpu6050Dmp目录)
- 电机控制模块 (Motor.c, Motor.h)
- PID控制算法 (pid.c, pid.h)
- 方向控制模块 (Direction.c, Direction.h)
- 线检测模块 (fine_line.c, fine_line.h)
- 按键控制模块 (key.c, key.h)
- 复杂任务调度 (task.c, task.h)
- 定时器PWM功能 (仅保留基本定时器)
- 编码器功能
- 所有传感器相关代码

❌ **移除的HAL库模块**:
- TIM PWM相关功能
- 编码器相关功能
- 复杂的中断处理

## 7. 依赖与风险

### 7.1 内部依赖项
- STM32F103C8 HAL库 (核心部分)
- OLED硬件连接正确 (I2C引脚: PB12-SCL, PB13-SDA)
- I2C2外设配置正确

### 7.2 外部依赖项
- ARM GCC工具链
- STM32 Ninja构建系统
- OLED硬件模块 (SSD1306或兼容)

### 7.3 潜在风险
🔴 **高风险**:
- 移除模块时可能影响OLED相关的依赖
- I2C配置可能需要调整
- GPIO配置可能需要精简

🟡 **中风险**:
- 构建系统需要更新 (build.ninja)
- 头文件依赖关系需要清理

🟢 **低风险**:
- OLED显示功能相对独立
- Hello World逻辑简单

### 7.4 风险缓解策略
1. **备份当前项目**: 在修改前创建完整备份
2. **渐进式移除**: 逐步移除模块，每次移除后测试编译
3. **依赖检查**: 仔细检查OLED模块的所有依赖
4. **功能验证**: 每次修改后验证OLED显示功能

## 8. 发布初步计划

### 8.1 开发阶段
1. **阶段1: 代码分析与备份** (预计10分钟)
   - 分析当前代码结构
   - 创建项目备份
   - 识别OLED模块的所有依赖

2. **阶段2: 模块移除** (预计15分钟)
   - 移除源文件和头文件
   - 更新构建配置
   - 清理include依赖

3. **阶段3: 代码重构** (预计10分钟)
   - 简化main.c函数
   - 实现Hello World显示逻辑
   - 清理不必要的全局变量

4. **阶段4: 测试验证** (预计10分钟)
   - 编译测试
   - 功能验证
   - 性能检查

### 8.2 验收标准
- [ ] 编译无错误无警告
- [ ] OLED正常显示"Hello World"
- [ ] 程序大小显著减小
- [ ] 代码结构清晰简洁
- [ ] 文档更新完整

### 8.3 回滚计划
如果重构失败，可以通过以下方式回滚：
1. 恢复备份的原始项目
2. 逐步恢复被移除的模块
3. 重新构建完整功能版本

---

**文档状态**: ✅ 完成  
**下一步**: 等待Mike批准后进入技术架构设计阶段
