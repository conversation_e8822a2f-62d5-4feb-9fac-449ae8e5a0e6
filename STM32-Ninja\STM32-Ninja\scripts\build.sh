#!/bin/bash
# STM32 Ninja - 编译脚本
# 负责项目的编译工作

# 加载通用函数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh" || exit 1

# 默认参数
BUILD_TYPE="Debug"
PROJECT_DIR=""
JOBS=$(($(nproc 2>/dev/null || echo 4) * 3 / 2))
GENERATOR="Ninja"

# 注意：已移除create_simple_ninja函数，因为不再需要make支持

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --type)
            BUILD_TYPE="$2"
            shift 2
            ;;
        --project)
            PROJECT_DIR="$2"
            shift 2
            ;;
        --jobs)
            JOBS="$2"
            shift 2
            ;;
        --generator)
            GENERATOR="$2"
            shift 2
            ;;
        *)
            error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 检查项目目录
if [ -z "$PROJECT_DIR" ]; then
    PROJECT_DIR="$(pwd)"
fi

cd "$PROJECT_DIR" || exit 1

# 检测项目类型
if ls *.uvprojx &>/dev/null || ls */*.uvprojx &>/dev/null; then
    BUILD_SYSTEM="Keil"
    info "检测到Keil工程"
    
    # 从.uvprojx文件名获取项目名称
    for f in *.uvprojx */*.uvprojx; do
        if [ -f "$f" ]; then
            PROJECT_NAME=$(basename "$f" .uvprojx)
            break
        fi
    done
elif [ -f "CMakeLists.txt" ]; then
    BUILD_SYSTEM="CMake"
    info "检测到CMake工程"
    
    # 从CMakeLists.txt获取项目名称
    PROJECT_NAME=$(grep -m1 "project(" CMakeLists.txt 2>/dev/null | \
                   sed -E 's/.*project\s*\(\s*([^)[:space:]]+).*/\1/' | \
                   tr -d '"' | tr -d ' ')
    if [ -z "$PROJECT_NAME" ]; then
        PROJECT_NAME=$(basename "$PROJECT_DIR")
    fi
else
    error "未找到支持的构建文件"
    error "支持的项目类型："
    error "  - Keil工程 (*.uvprojx)"
    error "  - CMake工程"
    exit 1
fi

info "编译项目: $PROJECT_NAME"
info "编译类型: $BUILD_TYPE"
info "使用线程: $JOBS"

# 设置工具链环境
setup_toolchain || exit 1

# 根据构建系统执行编译
if [ "$BUILD_SYSTEM" == "Keil" ] || [ "$BUILD_SYSTEM" == "Makefile" ]; then
    # 所有工程都使用 Ninja
    info "配置Ninja编译..."
    
    # 使用 STM32CubeCLT 自带的 Ninja
    NINJA_CMD="/d/STM32CubeCLT_1.18.0/Ninja/bin/ninja.exe"
    
    # 验证 Ninja
    if [ ! -f "$NINJA_CMD" ]; then
        error "未找到 Ninja: $NINJA_CMD"
        echo ""
        echo "请确保 STM32CubeCLT 已正确安装"
        echo "期望路径: /d/STM32CubeCLT_1.18.0/Ninja/bin/ninja.exe"
        exit 1
    fi
    
    info "使用 Ninja: $NINJA_CMD"
    
    # 如果没有 build.ninja，需要生成
    if [ ! -f "build.ninja" ]; then
        if [ "$BUILD_SYSTEM" == "Keil" ]; then
            info "检测到Keil工程，转换为Ninja格式..."
            "$SCRIPT_DIR/keil_to_ninja.sh" || {
                error "Keil工程转换失败"
                exit 1
            }
        elif [ "$BUILD_SYSTEM" == "Makefile" ]; then
            info "检测到Makefile工程，转换为Ninja格式..."
            # 使用智能转换脚本
            "$SCRIPT_DIR/makefile_to_ninja_smart.sh" || {
                # 如果失败，尝试简单转换
                "$SCRIPT_DIR/makefile_to_ninja_direct.sh" || {
                    error "Makefile转换失败"
                    exit 1
                }
            }
        fi
    fi
    
    # 验证 build.ninja
    if [ ! -f "build.ninja" ]; then
        error "无法生成 build.ninja"
        exit 1
    fi
    
    # 创建构建目录
    mkdir -p build
    
    # 使用 Ninja 编译
    info "开始Ninja高速编译..."
    # 使用更精确的计时方法
    if command -v perl &>/dev/null; then
        build_start=$(perl -MTime::HiRes=time -e 'printf "%.3f\n", time')
    else
        build_start=$(date +%s)
    fi
    
    # 执行ninja
    if ! $NINJA_CMD -j $JOBS 2>&1 | tee build_error.log; then
        error "编译失败，尝试智能修复..."
        
        # 首次尝试：运行自动修复脚本
        if [ -f "$SCRIPT_DIR/auto_fix_compile.sh" ]; then
            info "运行自动修复..."
            if "$SCRIPT_DIR/auto_fix_compile.sh" build_error.log; then
                # 修复成功，重新编译
                info "重新编译..."
                if $NINJA_CMD -j $JOBS; then
                    success "编译成功（经过自动修复）！"
                    # 继续执行后续步骤
                else
                    # 第二次尝试：运行智能构建
                    warn "自动修复后仍然失败，尝试深度修复..."
                    "$SCRIPT_DIR/smart_build_v2.sh" build
                    exit $?
                fi
            else
                # 修复失败，尝试其他方法
                warn "自动修复失败，尝试智能构建..."
                "$SCRIPT_DIR/smart_build_v2.sh" build
                exit $?
            fi
        else
            # 没有自动修复脚本，使用原逻辑
            # 检查是否是启动文件格式问题
            if grep -q "bad instruction\|Error: junk at end of line" build_error.log && grep -q "startup.*\.s" build_error.log; then
                warn "检测到Keil格式启动文件问题"
                "$SCRIPT_DIR/smart_build_v2.sh" build
                exit $?
            fi
            
            # 检查是否是缺少头文件
            if grep -q "undeclared\|undefined" build_error.log; then
                warn "检测到缺少定义问题"
                "$SCRIPT_DIR/smart_build_v2.sh" build
                exit $?
            fi
            
            exit 1
        fi
    fi
    
else
    # CMake工程编译（原有逻辑）
    # 创建构建目录
    mkdir -p build
    
    # 配置CMake
    info "配置构建系统..."
    cmake_args=(
        -B build
        -G "$GENERATOR"
        -DCMAKE_BUILD_TYPE="$BUILD_TYPE"
        -DCMAKE_TOOLCHAIN_FILE=cmake/toolchain.cmake
        -DCMAKE_EXPORT_COMPILE_COMMANDS=ON
    )
    
    # 如果有ccache，启用它
    if command -v ccache &>/dev/null; then
        cmake_args+=(-DCMAKE_C_COMPILER_LAUNCHER=ccache)
        cmake_args+=(-DCMAKE_CXX_COMPILER_LAUNCHER=ccache)
        info "启用编译缓存 (ccache)"
    fi
    
    # 执行CMake配置
    if ! cmake "${cmake_args[@]}"; then
        error "CMake配置失败"
        exit 1
    fi
    
    # 开始编译
    info "开始编译..."
    # 使用更精确的计时方法
    if command -v perl &>/dev/null; then
        build_start=$(perl -MTime::HiRes=time -e 'printf "%.3f\n", time')
    else
        build_start=$(date +%s)
    fi
    
    if [[ "$GENERATOR" == "Ninja" ]]; then
        # Ninja编译
        if ! ninja -C build -j "$JOBS"; then
            error "编译失败"
            exit 1
        fi
    else
        # Make编译
        if ! cmake --build build --parallel "$JOBS"; then
            error "编译失败"
            exit 1
        fi
    fi
fi

# 结束计时
if command -v perl &>/dev/null; then
    build_end=$(perl -MTime::HiRes=time -e 'printf "%.3f\n", time')
    # 使用awk计算时间差（毫秒级）
    build_time=$(awk "BEGIN {printf \"%.3f\", $build_end - $build_start}")
    build_time_ms=$(awk "BEGIN {printf \"%.0f\", ($build_end - $build_start) * 1000}")
else
    build_end=$(date +%s)
    build_time=$((build_end - build_start))
    build_time_ms=$((build_time * 1000))
fi

# 显示编译结果
echo ""
if command -v perl &>/dev/null; then
    formatted_time=$(format_time $build_time_ms)
    success "编译完成！用时: $formatted_time"
else
    success "编译完成！用时: ${build_time}秒"
fi

# 显示内存使用和输出文件
if [ "$BUILD_SYSTEM" == "Keil" ]; then
    # Keil/Ninja工程的输出文件在build目录
    HEX_FILE="build/$PROJECT_NAME.hex"
    if [ -f "$HEX_FILE" ]; then
        echo ""
        success "输出文件: $HEX_FILE"
    fi
else
    # CMake工程
    if [ -f "build/$PROJECT_NAME.elf" ]; then
        # 生成hex文件
        info "生成HEX文件..."
        arm-none-eabi-objcopy -O ihex "build/$PROJECT_NAME.elf" "build/$PROJECT_NAME.hex"
        echo ""
        success "输出文件: build/$PROJECT_NAME.hex"
    fi
fi

# 显示MICU结束LOGO
echo ""
echo ""
echo "    __  __ ___ ____ _   _ "
echo "   |  \\/  |_ _/ ___| | | |"
echo "   | |\\/| || | |   | | | |"
echo "   | |  | || | |___| |_| |"
echo "   |_|  |_|___\\____|\\___/ "
echo ""
echo "   编译任务圆满完成！"
echo ""

exit 0