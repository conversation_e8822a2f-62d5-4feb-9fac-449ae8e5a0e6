#!/bin/bash
# STM32 Ninja - Keil启动文件转换器
# 将Keil格式的启动文件转换为GNU汇编格式

# 输入输出文件
input_file="$1"
output_file="${2:-startup_gnu.s}"

if [ -z "$input_file" ]; then
    echo "用法: $0 <keil_startup.s> [output.s]"
    exit 1
fi

if [ ! -f "$input_file" ]; then
    echo "错误: 文件不存在: $input_file"
    exit 1
fi

# 从文件中提取MCU信息
mcu_name=$(grep -i "STM32F" "$input_file" | head -1 | grep -o "STM32F[0-9]*" | head -1)
if [ -z "$mcu_name" ]; then
    mcu_name="STM32F4xx"
fi

echo "转换启动文件: $input_file -> $output_file"
echo "检测到MCU: $mcu_name"

# 创建GNU格式的启动文件
cat > "$output_file" << 'EOF'
/**
 * @file      startup_stm32.s
 * @brief     GNU startup file converted from Keil format
 */

  .syntax unified
  .cpu cortex-m4
  .fpu fpv4-sp-d16
  .thumb

.global  g_pfnVectors
.global  Default_Handler

/* start address for the initialization values of the .data section */
.word  _sidata
/* start address for the .data section */
.word  _sdata
/* end address for the .data section */
.word  _edata
/* start address for the .bss section */
.word  _sbss
/* end address for the .bss section */
.word  _ebss

  .section  .text.Reset_Handler
  .weak  Reset_Handler
  .type  Reset_Handler, %function
Reset_Handler:
  ldr   sp, =_estack      /* set stack pointer */

/* Copy the data segment initializers from flash to SRAM */
  ldr r0, =_sdata
  ldr r1, =_edata
  ldr r2, =_sidata
  movs r3, #0
  b LoopCopyDataInit

CopyDataInit:
  ldr r4, [r2, r3]
  str r4, [r0, r3]
  adds r3, r3, #4

LoopCopyDataInit:
  adds r4, r0, r3
  cmp r4, r1
  bcc CopyDataInit
  
/* Zero fill the bss segment */
  ldr r2, =_sbss
  ldr r4, =_ebss
  movs r3, #0
  b LoopFillZerobss

FillZerobss:
  str  r3, [r2]
  adds r2, r2, #4

LoopFillZerobss:
  cmp r2, r4
  bcc FillZerobss

/* Call the clock system initialization function */
  bl  SystemInit
/* Call static constructors */
  bl __libc_init_array
/* Call the application's entry point */
  bl  main
  bx  lr
.size  Reset_Handler, .-Reset_Handler

/**
 * @brief  Default interrupt handler for unused interrupts
 */
  .section  .text.Default_Handler,"ax",%progbits
Default_Handler:
Infinite_Loop:
  b  Infinite_Loop
  .size  Default_Handler, .-Default_Handler

/******************************************************************************
* The STM32 vector table
******************************************************************************/
  .section  .isr_vector,"a",%progbits
  .type  g_pfnVectors, %object
  .size  g_pfnVectors, .-g_pfnVectors

g_pfnVectors:
  .word  _estack
  .word  Reset_Handler
  .word  NMI_Handler
  .word  HardFault_Handler
  .word  MemManage_Handler
  .word  BusFault_Handler
  .word  UsageFault_Handler
  .word  0
  .word  0
  .word  0
  .word  0
  .word  SVC_Handler
  .word  DebugMon_Handler
  .word  0
  .word  PendSV_Handler
  .word  SysTick_Handler
EOF

# 从Keil文件中提取中断向量
echo "  /* External Interrupts */" >> "$output_file"

# 提取DCD指令行，转换为.word格式
grep -E "^\s*DCD\s+" "$input_file" | grep -v "__initial_sp\|Reset_Handler\|NMI_Handler\|HardFault\|MemManage\|BusFault\|UsageFault\|SVC_Handler\|DebugMon\|PendSV\|SysTick" | while read -r line; do
    # 提取中断处理函数名
    handler=$(echo "$line" | sed -E 's/.*DCD\s+([A-Za-z0-9_]+).*/\1/')
    if [ -n "$handler" ] && [ "$handler" != "0" ]; then
        echo "  .word  $handler" >> "$output_file"
    else
        echo "  .word  0" >> "$output_file"
    fi
done

# 添加弱符号定义
cat >> "$output_file" << 'EOF'

/*******************************************************************************
* Provide weak aliases for each Exception handler to the Default_Handler
*******************************************************************************/
  .weak      NMI_Handler
  .thumb_set NMI_Handler,Default_Handler

  .weak      HardFault_Handler
  .thumb_set HardFault_Handler,Default_Handler

  .weak      MemManage_Handler
  .thumb_set MemManage_Handler,Default_Handler

  .weak      BusFault_Handler
  .thumb_set BusFault_Handler,Default_Handler

  .weak      UsageFault_Handler
  .thumb_set UsageFault_Handler,Default_Handler

  .weak      SVC_Handler
  .thumb_set SVC_Handler,Default_Handler

  .weak      DebugMon_Handler
  .thumb_set DebugMon_Handler,Default_Handler

  .weak      PendSV_Handler
  .thumb_set PendSV_Handler,Default_Handler

  .weak      SysTick_Handler
  .thumb_set SysTick_Handler,Default_Handler
EOF

# 从Keil文件提取所有中断处理函数，添加弱符号
grep -E "^\s*(EXPORT|export)\s+[A-Za-z0-9_]+_IRQHandler" "$input_file" | while read -r line; do
    handler=$(echo "$line" | sed -E 's/.*(EXPORT|export)\s+([A-Za-z0-9_]+_IRQHandler).*/\2/')
    if [ -n "$handler" ]; then
        cat >> "$output_file" << EOF

  .weak      $handler
  .thumb_set $handler,Default_Handler
EOF
    fi
done

echo "" >> "$output_file"

echo "转换完成: $output_file"