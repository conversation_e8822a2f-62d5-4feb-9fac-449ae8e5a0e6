#!/bin/bash
# STM32 Ninja - AI API服务

# 加载通用函数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh" || exit 1

# API配置
API_PORT=${API_PORT:-8080}
API_LOG="logs/api.log"
PID_FILE="logs/api.pid"

# 创建日志目录
mkdir -p logs

# HTTP响应函数
http_response() {
    local status="$1"
    local content_type="${2:-text/plain}"
    local body="$3"
    
    echo -e "HTTP/1.1 $status\r"
    echo -e "Content-Type: $content_type\r"
    echo -e "Content-Length: ${#body}\r"
    echo -e "Access-Control-Allow-Origin: *\r"
    echo -e "Connection: close\r"
    echo -e "\r"
    echo -n "$body"
}

# JSON响应
json_response() {
    local status="$1"
    local message="$2"
    local data="$3"
    
    local json="{\"status\":\"$status\",\"message\":\"$message\""
    if [ -n "$data" ]; then
        json="$json,\"data\":$data"
    fi
    json="$json}"
    
    http_response "200 OK" "application/json" "$json"
}

# 处理编译请求
handle_compile() {
    local project_path="$1"
    local build_type="${2:-Debug}"
    
    echo "[$(date)] 编译请求: $project_path (${build_type})" >> "$API_LOG"
    
    if [ ! -d "$project_path" ]; then
        json_response "error" "项目目录不存在" 
        return
    fi
    
    # 执行编译
    local output=$(cd "$project_path" && "$SCRIPT_DIR/build.sh" --type "$build_type" 2>&1)
    local result=$?
    
    if [ $result -eq 0 ]; then
        json_response "success" "编译成功" "{\"output\":\"${output//\"/\\\"}\"}"
    else
        json_response "error" "编译失败" "{\"output\":\"${output//\"/\\\"}\"}"
    fi
}

# 处理烧录请求
handle_flash() {
    local project_path="$1"
    
    echo "[$(date)] 烧录请求: $project_path" >> "$API_LOG"
    
    if [ ! -d "$project_path" ]; then
        json_response "error" "项目目录不存在"
        return
    fi
    
    # 执行烧录
    local output=$(cd "$project_path" && "$SCRIPT_DIR/flash.sh" 2>&1)
    local result=$?
    
    if [ $result -eq 0 ]; then
        json_response "success" "烧录成功" "{\"output\":\"${output//\"/\\\"}\"}"
    else
        json_response "error" "烧录失败" "{\"output\":\"${output//\"/\\\"}\"}"
    fi
}

# 处理状态请求
handle_status() {
    local project_path="$1"
    
    if [ -n "$project_path" ] && [ -d "$project_path" ]; then
        cd "$project_path"
        
        local project_name=""
        if [ -f "CMakeLists.txt" ]; then
            project_name=$(grep -m1 "project(" CMakeLists.txt 2>/dev/null | \
                          sed -E 's/.*project\s*\(\s*([^)[:space:]]+).*/\1/' | \
                          tr -d '"' | tr -d ' ')
        fi
        
        local has_elf=false
        [ -f "build/*.elf" ] && has_elf=true
        
        local data="{\"project_name\":\"$project_name\",\"has_build\":$has_elf}"
        json_response "success" "状态获取成功" "$data"
    else
        # 返回API服务状态
        local data="{\"api_version\":\"1.0\",\"port\":$API_PORT,\"status\":\"running\"}"
        json_response "success" "API服务运行中" "$data"
    fi
}

# 处理请求
handle_request() {
    local request=""
    read -r request
    
    # 解析请求
    local method=$(echo "$request" | cut -d' ' -f1)
    local path=$(echo "$request" | cut -d' ' -f2)
    
    # 读取请求头
    local content_length=0
    while read -r header; do
        header=$(echo "$header" | tr -d '\r')
        [ -z "$header" ] && break
        
        if [[ "$header" =~ ^Content-Length:\ (.*)$ ]]; then
            content_length="${BASH_REMATCH[1]}"
        fi
    done
    
    # 读取请求体
    local body=""
    if [ "$content_length" -gt 0 ]; then
        body=$(dd bs=1 count="$content_length" 2>/dev/null)
    fi
    
    echo "[$(date)] $method $path" >> "$API_LOG"
    
    # 路由处理
    case "$method $path" in
        "GET /")
            http_response "200 OK" "text/html" "<h1>STM32 Ninja API</h1><p>API服务运行中</p>"
            ;;
            
        "GET /status")
            handle_status
            ;;
            
        "POST /compile")
            # 解析JSON参数（简单实现）
            local project_path=$(echo "$body" | grep -o '"project_path":"[^"]*' | cut -d'"' -f4)
            local build_type=$(echo "$body" | grep -o '"build_type":"[^"]*' | cut -d'"' -f4)
            handle_compile "$project_path" "$build_type"
            ;;
            
        "POST /flash")
            local project_path=$(echo "$body" | grep -o '"project_path":"[^"]*' | cut -d'"' -f4)
            handle_flash "$project_path"
            ;;
            
        "OPTIONS "*) 
            # CORS预检请求
            echo -e "HTTP/1.1 200 OK\r"
            echo -e "Access-Control-Allow-Origin: *\r"
            echo -e "Access-Control-Allow-Methods: GET, POST, OPTIONS\r"
            echo -e "Access-Control-Allow-Headers: Content-Type\r"
            echo -e "\r"
            ;;
            
        *)
            json_response "error" "未知请求: $method $path"
            ;;
    esac
}

# 启动服务器
start_server() {
    info "启动API服务器..."
    
    # 检查是否已经运行
    if [ -f "$PID_FILE" ]; then
        local old_pid=$(cat "$PID_FILE")
        if kill -0 "$old_pid" 2>/dev/null; then
            error "API服务器已经在运行 (PID: $old_pid)"
            exit 1
        fi
    fi
    
    # 保存PID
    echo $$ > "$PID_FILE"
    
    success "API服务器启动成功"
    echo "监听端口: $API_PORT"
    echo "日志文件: $API_LOG"
    echo ""
    echo "API端点："
    echo "  GET  /status       - 获取状态"
    echo "  POST /compile      - 编译项目"
    echo "  POST /flash        - 烧录程序"
    echo ""
    echo "示例请求："
    echo '  curl -X POST http://localhost:8080/compile \'
    echo '    -H "Content-Type: application/json" \'
    echo '    -d {"project_path":"/path/to/project","build_type":"Debug"}'
    echo ""
    
    # 主循环
    while true; do
        # 使用nc监听请求
        echo "$request" | nc -l -p "$API_PORT" -q 1 | handle_request
    done
}

# 简单的HTTP服务器实现（不依赖nc）
simple_server() {
    info "启动简单API服务器..."
    
    # 创建命名管道
    local pipe="/tmp/ninja_api_$$"
    mkfifo "$pipe"
    trap "rm -f $pipe" EXIT
    
    success "API服务器启动成功"
    echo "监听端口: $API_PORT"
    echo ""
    
    # 使用bash内置的/dev/tcp
    while true; do
        # 等待连接
        exec 3<>/dev/tcp/localhost/"$API_PORT" 2>/dev/null || {
            # 如果/dev/tcp不可用，使用Python
            python3 -m http.server "$API_PORT" --bind localhost 2>/dev/null || \
            python -m SimpleHTTPServer "$API_PORT" 2>/dev/null || {
                error "无法启动服务器，请安装Python或nc"
                exit 1
            }
        }
        
        # 处理请求
        handle_request <&3 >&3
        
        # 关闭连接
        exec 3<&-
        exec 3>&-
    done
}

# 主程序
main() {
    case "$1" in
        stop)
            if [ -f "$PID_FILE" ]; then
                local pid=$(cat "$PID_FILE")
                if kill "$pid" 2>/dev/null; then
                    success "API服务器已停止"
                    rm -f "$PID_FILE"
                else
                    warn "无法停止服务器 (PID: $pid)"
                fi
            else
                info "API服务器未运行"
            fi
            ;;
        *)
            # 尝试使用nc，如果不可用则使用简单服务器
            if command -v nc &>/dev/null; then
                start_server
            else
                simple_server
            fi
            ;;
    esac
}

# 信号处理
trap 'echo ""; info "停止API服务器..."; rm -f "$PID_FILE"; exit 0' INT TERM

# 执行主程序
main "$@"