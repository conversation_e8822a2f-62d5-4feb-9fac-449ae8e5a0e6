{"name": "Ide To Keil", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "Application", "files": [], "folders": [{"name": "MDK-ARM", "files": [{"path": "startup_stm32f103xb.s"}], "folders": []}, {"name": "User", "files": [], "folders": [{"name": "Core", "files": [{"path": "Task/Src/main.c"}, {"path": "../Core/Src/gpio.c"}, {"path": "../Core/Src/i2c.c"}, {"path": "../Core/Src/tim.c"}, {"path": "../Core/Src/stm32f1xx_it.c"}, {"path": "../Core/Src/stm32f1xx_hal_msp.c"}], "folders": []}]}]}, {"name": "Drivers", "files": [], "folders": [{"name": "STM32F1xx_HAL_Driver", "files": [{"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_i2c.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim_ex.c"}], "folders": []}, {"name": "CMSIS", "files": [{"path": "../Core/Src/system_stm32f1xx.c"}], "folders": []}]}, {"name": "SRC", "files": [{"path": "Task/Src/Direction.c"}, {"path": "Task/Src/fine_line.c"}, {"path": "Task/Src/key.c"}, {"path": "Task/Src/Motor.c"}, {"path": "Task/Src/OLED.c"}, {"path": "Task/Src/pid.c"}, {"path": "Task/Src/task.c"}], "folders": []}, {"name": "inc", "files": [{"path": "Task/Inc/Direction.h"}, {"path": "Task/Inc/fine_line.h"}, {"path": "Task/Inc/key.h"}, {"path": "Task/Inc/main.h"}, {"path": "Task/Inc/Motor.h"}, {"path": "Task/Inc/OLED.h"}, {"path": "Task/Inc/OLED_Font.h"}, {"path": "Task/Inc/pid.h"}, {"path": "Task/Inc/task.h"}, {"path": "Task/Inc/tim.h"}], "folders": []}, {"name": "MPU6050", "files": [{"path": "Task/Mpu6050Dmp/dmpKey.h"}, {"path": "Task/Mpu6050Dmp/dmpmap.h"}, {"path": "Task/Mpu6050Dmp/inv_mpu.c"}, {"path": "Task/Mpu6050Dmp/inv_mpu.h"}, {"path": "Task/Mpu6050Dmp/inv_mpu_dmp_motion_driver.c"}, {"path": "Task/Mpu6050Dmp/inv_mpu_dmp_motion_driver.h"}, {"path": "Task/Mpu6050Dmp/MPU6050.c"}, {"path": "Task/Mpu6050Dmp/MPU6050.h"}], "folders": []}, {"name": "::CMSIS", "files": [], "folders": []}]}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "3ffd5373f3a1282e80f81c0fd1a4a94a"}, "targets": {"Ide To Keil": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M3", "archExtensions": "", "floatingPointHardware": "none", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x5000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x10000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "JLink", "uploadConfig": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}, "uploadConfigMap": {}, "custom_dep": {"name": "default", "incList": ["../Core/Inc", "../Drivers/STM32F1xx_HAL_Driver/Inc", "../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy", "../Drivers/CMSIS/Device/ST/STM32F1xx/Include", "../Drivers/CMSIS/Include", "Task/Src", "Task/Inc", "Task/Mpu6050Dmp", ".cmsis/include", "RTE/_Ide To Keil"], "libList": [], "defineList": ["USE_HAL_DRIVER", "STM32F103xB"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": false, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-3", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "xo-base": "", "ro-base": "", "rw-base": ""}}}}}, "version": "3.6"}