#!/bin/bash
# 诊断包含路径问题

echo "======================================"
echo "  包含路径诊断工具"
echo "======================================"
echo ""

# 测试编译单个文件
test_compile() {
    local src_file="$1"
    local work_dir="$2"
    
    echo "测试编译: $src_file"
    echo "工作目录: $work_dir"
    echo ""
    
    cd "$work_dir" || exit 1
    
    # 从build.ninja提取编译命令
    if [ -f "build.ninja" ]; then
        echo "从build.ninja提取编译命令..."
        local cc=$(grep "^cc =" build.ninja | cut -d'=' -f2 | tr -d ' ')
        local cflags=$(grep "^cflags =" build.ninja | cut -d'=' -f2-)
        local includes=$(grep "^includes =" build.ninja | cut -d'=' -f2-)
        local defines=$(grep "^defines =" build.ninja | cut -d'=' -f2-)
        
        echo "编译器: $cc"
        echo "编译标志: $cflags"
        echo "包含路径: $includes"
        echo "宏定义: $defines"
        echo ""
        
        # 测试每个包含路径
        echo "验证包含路径："
        for inc in $includes; do
            if [[ "$inc" == -I* ]]; then
                local path="${inc#-I}"
                if [ -d "$path" ]; then
                    echo "  ✓ $inc (存在)"
                    # 检查ebtn相关文件
                    if [[ "$path" == *ebtn* ]]; then
                        echo "    检查ebtn文件："
                        [ -f "$path/ebtn.h" ] && echo "      ✓ ebtn.h 存在"
                        [ -f "$path/ebtn.c" ] && echo "      ✓ ebtn.c 存在"
                        # 搜索EBTN_CFG_COMBO_PRIORITY
                        if [ -f "$path/ebtn.h" ]; then
                            if grep -q "EBTN_CFG_COMBO_PRIORITY" "$path/ebtn.h"; then
                                echo "      ✓ 找到 EBTN_CFG_COMBO_PRIORITY 定义"
                                grep -n "EBTN_CFG_COMBO_PRIORITY" "$path/ebtn.h" | head -1
                            fi
                            if grep -q "ebtn_set_config" "$path/ebtn.h"; then
                                echo "      ✓ 找到 ebtn_set_config 声明"
                                grep -n "ebtn_set_config" "$path/ebtn.h" | head -1
                            fi
                        fi
                    fi
                else
                    echo "  ✗ $inc (不存在)"
                fi
            fi
        done
        echo ""
        
        # 预处理测试
        echo "预处理测试..."
        local test_cmd="$cc -E $cflags $includes $defines $src_file -o test_preprocess.i"
        echo "命令: $test_cmd"
        echo ""
        
        if $test_cmd 2>preprocess_error.log; then
            echo "✓ 预处理成功"
            # 检查预处理后的文件
            if grep -q "EBTN_CFG_COMBO_PRIORITY" test_preprocess.i; then
                echo "✓ 预处理后找到 EBTN_CFG_COMBO_PRIORITY"
            else
                echo "✗ 预处理后未找到 EBTN_CFG_COMBO_PRIORITY"
            fi
        else
            echo "✗ 预处理失败"
            cat preprocess_error.log
        fi
        
        # 清理
        rm -f test_preprocess.i preprocess_error.log
    else
        echo "错误: 未找到 build.ninja"
    fi
}

# 主程序
if [ $# -lt 2 ]; then
    echo "用法: $0 <源文件> <工作目录>"
    echo "例如: $0 ../APP/btn_app.c /h/code/GD32/GD32_DEMO_05/MDK-ARM"
    exit 1
fi

test_compile "$1" "$2"