#!/bin/bash
# STM32 Ninja - 全局安装脚本

# 默认安装路径
DEFAULT_INSTALL_PATH="/d/STM32Ninja"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 输出函数
info() { echo -e "${BLUE}[信息]${NC} $1"; }
success() { echo -e "${GREEN}[成功]${NC} $1"; }
warn() { echo -e "${YELLOW}[警告]${NC} $1"; }
error() { echo -e "${RED}[错误]${NC} $1"; }

# 显示Logo
show_logo() {
    echo -e "${BLUE}"
    cat << 'EOF'
     _____ _______ __  __ ____ ___    _   _ _       _       
    / ____|__   __|  \/  |___ \__ \  | \ | (_)     (_)      
   | (___    | |  | \  / | __) | ) | |  \| |_ _ __  _  __ _ 
    \___ \   | |  | |\/| ||__ < / /  | . ` | | '_ \| |/ _` |
    ____) |  | |  | |  | |___) / /_  | |\  | | | | | | (_| |
   |_____/   |_|  |_|  |_|____/____| |_| \_|_|_| |_|_|\__,_|
EOF
    echo -e "${NC}"
    echo "    全局安装程序 v2.0"
    echo ""
}

# 检查管理员权限（Windows下不需要sudo）
check_permissions() {
    # Git Bash下检查是否可以写入系统目录
    if [ ! -w "/d" ]; then
        warn "可能需要管理员权限来安装到系统目录"
    fi
}

# 选择安装路径
select_install_path() {
    echo "选择安装位置："
    echo "  1) 默认位置 ($DEFAULT_INSTALL_PATH)"
    echo "  2) 用户目录 (~/.stm32ninja)"
    echo "  3) 自定义位置"
    echo ""
    echo -n "请选择 [1-3]: "
    read -n 1 choice
    echo ""
    
    case $choice in
        1)
            INSTALL_PATH="$DEFAULT_INSTALL_PATH"
            ;;
        2)
            INSTALL_PATH="$HOME/.stm32ninja"
            ;;
        3)
            echo -n "请输入安装路径: "
            read INSTALL_PATH
            ;;
        *)
            INSTALL_PATH="$DEFAULT_INSTALL_PATH"
            ;;
    esac
    
    # 转换为绝对路径
    INSTALL_PATH=$(cd "$(dirname "$INSTALL_PATH")" 2>/dev/null && pwd)/$(basename "$INSTALL_PATH")
    
    info "安装路径: $INSTALL_PATH"
}

# 安装核心文件
install_core() {
    info "安装核心文件..."
    
    # 创建目录结构
    mkdir -p "$INSTALL_PATH"/{bin,scripts,config,templates,docs}
    
    # 复制脚本文件
    cp -r scripts/* "$INSTALL_PATH/scripts/" 2>/dev/null
    
    # 创建主执行文件
    cat > "$INSTALL_PATH/bin/stm32ninja" << 'EOF'
#!/bin/bash
# STM32 Ninja 主命令

NINJA_HOME="$(dirname "$(dirname "$(readlink -f "$0")")")"
export STM32_NINJA_HOME="$NINJA_HOME"

# 根据命令执行不同的脚本
case "$1" in
    init|version|--version|-v|help|--help|-h)
        exec "$NINJA_HOME/scripts/init_project.sh" "$@"
        ;;
    *)
        # 其他命令交给用户提示
        echo "用法: stm32ninja <命令> [参数]"
        echo "运行 'stm32ninja help' 查看帮助"
        exit 1
        ;;
esac
EOF
    chmod +x "$INSTALL_PATH/bin/stm32ninja"
    
    # 直接复制ninja.sh作为主程序（已经是v2版本）
    cp ninja.sh "$INSTALL_PATH/scripts/ninja_main.sh"
    chmod +x "$INSTALL_PATH/scripts/ninja_main.sh"
    
    # 复制模板文件
    if [ -d "templates" ]; then
        cp -r templates/* "$INSTALL_PATH/templates/"
    fi
    
    # 复制文档
    if [ -d "docs" ]; then
        cp -r docs/* "$INSTALL_PATH/docs/"
    fi
    
    success "核心文件安装完成"
}

# 配置环境变量
setup_environment() {
    info "配置环境变量..."
    
    # 检测shell配置文件
    local shell_rc=""
    if [ -f "$HOME/.bashrc" ]; then
        shell_rc="$HOME/.bashrc"
    elif [ -f "$HOME/.bash_profile" ]; then
        shell_rc="$HOME/.bash_profile"
    else
        shell_rc="$HOME/.bashrc"
    fi
    
    # 检查是否已经添加
    if ! grep -q "STM32_NINJA_HOME" "$shell_rc" 2>/dev/null; then
        echo "" >> "$shell_rc"
        echo "# STM32 Ninja" >> "$shell_rc"
        echo "export STM32_NINJA_HOME=\"$INSTALL_PATH\"" >> "$shell_rc"
        echo "export PATH=\"\$STM32_NINJA_HOME/bin:\$PATH\"" >> "$shell_rc"
        
        success "环境变量已添加到 $shell_rc"
        info "请运行 'source $shell_rc' 或重新打开终端"
    else
        info "环境变量已存在"
    fi
}

# 创建项目初始化脚本
create_init_script() {
    cat > "$INSTALL_PATH/scripts/init_project.sh" << 'EOF'
#!/bin/bash
# STM32 Ninja - 项目初始化脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 输出函数
info() { echo -e "${BLUE}[信息]${NC} $1"; }
success() { echo -e "${GREEN}[成功]${NC} $1"; }
warn() { echo -e "${YELLOW}[警告]${NC} $1"; }
error() { echo -e "${RED}[错误]${NC} $1"; }

# 检测项目类型
detect_project_type() {
    local dir="${1:-.}"
    
    # 检测Keil工程 - 改进的检测逻辑
    if find "$dir" -maxdepth 2 -name "*.uvprojx" -type f 2>/dev/null | grep -q .; then
        echo "keil"
        return
    fi
    
    # 检测CMake工程
    if [ -f "$dir/CMakeLists.txt" ]; then
        echo "cmake"
        return
    fi
    
    # 检测Makefile工程
    if [ -f "$dir/Makefile" ]; then
        echo "makefile"
        return
    fi
    
    echo "unknown"
}

# 在项目目录中初始化STM32 Ninja支持
init_project() {
    local project_dir="${1:-.}"
    
    # 转到项目目录
    cd "$project_dir" || {
        error "无法进入目录: $project_dir"
        exit 1
    }
    
    info "检测项目类型..."
    local project_type=$(detect_project_type ".")
    
    case "$project_type" in
        keil)
            success "检测到Keil工程"
            local uvprojx_file=$(find . -maxdepth 2 -name "*.uvprojx" -type f | head -1)
            info "工程文件: $uvprojx_file"
            ;;
        cmake)
            success "检测到CMake工程"
            ;;
        makefile)
            success "检测到Makefile工程"
            ;;
        unknown)
            warn "未检测到已知的工程类型"
            echo "支持的工程类型："
            echo "  - Keil (*.uvprojx)"
            echo "  - CMake (CMakeLists.txt)"
            echo "  - Makefile"
            ;;
    esac
    
    # 检查是否已初始化
    if [ -f ".stm32ninja" ]; then
        warn "项目已初始化"
        echo -n "是否重新初始化? (y/N): "
        read -n 1 confirm
        echo ""
        if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
            return 0
        fi
    fi
    
    # 创建项目配置
    cat > ".stm32ninja" << EOL
# STM32 Ninja 项目配置
PROJECT_TYPE=$project_type
NINJA_VERSION=2.0
INIT_DATE=$(date +%Y-%m-%d)
PROJECT_DIR=$(pwd)
EOL
    
    # 创建ninja.sh脚本（不是符号链接，因为Windows下符号链接有问题）
    cat > "ninja.sh" << 'NINJA_SH'
#!/bin/bash
# STM32 Ninja - 项目启动脚本

# 获取项目目录
PROJECT_DIR="$(dirname "$(readlink -f "$0" 2>/dev/null || realpath "$0" 2>/dev/null || echo "$0")")"

# 查找全局安装
if [ -z "$STM32_NINJA_HOME" ]; then
    # 尝试常见位置
    for path in "/d/STM32Ninja" "$HOME/.stm32ninja" "/opt/stm32ninja"; do
        if [ -f "$path/scripts/ninja_main.sh" ]; then
            export STM32_NINJA_HOME="$path"
            break
        fi
    done
fi

if [ -z "$STM32_NINJA_HOME" ] || [ ! -f "$STM32_NINJA_HOME/scripts/ninja_main.sh" ]; then
    echo "[错误] 未找到STM32 Ninja全局安装"
    echo "请先运行全局安装脚本"
    exit 1
fi

# 设置项目环境
export NINJA_PROJECT_DIR="$PROJECT_DIR"
cd "$PROJECT_DIR"

# 运行主程序
exec "$STM32_NINJA_HOME/scripts/ninja_main.sh" "$@"
NINJA_SH
    chmod +x "ninja.sh"
    
    success "项目初始化完成！"
    echo ""
    echo "项目信息："
    echo "  类型: $project_type"
    echo "  目录: $(pwd)"
    echo ""
    echo "使用方法："
    echo "  ./ninja.sh          # 启动交互式菜单"
    echo "  ./ninja.sh build    # 直接编译"
    echo "  ./ninja.sh flash    # 直接烧录"
    echo "  ./ninja.sh list     # 列出编译文件"
    echo "  ./ninja.sh add      # 添加文件到编译"
    
    # 创建配置目录
    mkdir -p config
    
    # 如果是Keil项目，询问是否生成build.ninja
    if [ "$project_type" = "keil" ]; then
        echo ""
        echo -n "是否立即生成Ninja编译文件? (Y/n): "
        read -n 1 gen_ninja
        echo ""
        if [ "$gen_ninja" != "n" ] && [ "$gen_ninja" != "N" ]; then
            if [ -f "$STM32_NINJA_HOME/scripts/keil_to_ninja_smart.sh" ]; then
                "$STM32_NINJA_HOME/scripts/keil_to_ninja_smart.sh"
            elif [ -f "$STM32_NINJA_HOME/scripts/keil_to_ninja.sh" ]; then
                "$STM32_NINJA_HOME/scripts/keil_to_ninja.sh"
            else
                warn "未找到Keil转换脚本，请手动运行 './ninja.sh build'"
            fi
        fi
    fi
}

# 主函数
case "$1" in
    init)
        init_project "$2"
        ;;
    version|--version|-v)
        echo "STM32 Ninja v2.0"
        ;;
    help|--help|-h)
        echo "STM32 Ninja - 全局命令"
        echo ""
        echo "用法:"
        echo "  stm32ninja init [目录]   - 在指定目录初始化项目"
        echo "  stm32ninja version       - 显示版本信息"
        echo "  stm32ninja help          - 显示帮助信息"
        echo ""
        echo "示例:"
        echo "  cd /path/to/project"
        echo "  stm32ninja init"
        ;;
    *)
        echo "用法: stm32ninja <命令> [参数]"
        echo "运行 'stm32ninja help' 查看帮助"
        ;;
esac
EOF
    chmod +x "$INSTALL_PATH/scripts/init_project.sh"
}

# 创建项目专用脚本
create_project_script() {
    cat > "$INSTALL_PATH/scripts/ninja_project.sh" << 'EOF'
#!/bin/bash
# STM32 Ninja - 项目启动脚本

# 获取项目目录
PROJECT_DIR="$(dirname "$(readlink -f "$0")")"

# 检查全局安装
if [ -z "$STM32_NINJA_HOME" ]; then
    # 尝试查找安装位置
    for path in "/d/STM32Ninja" "$HOME/.stm32ninja" "/opt/stm32ninja"; do
        if [ -f "$path/scripts/ninja_main.sh" ]; then
            export STM32_NINJA_HOME="$path"
            break
        fi
    done
fi

if [ -z "$STM32_NINJA_HOME" ] || [ ! -f "$STM32_NINJA_HOME/scripts/ninja_main.sh" ]; then
    echo "[错误] 未找到STM32 Ninja安装"
    echo "请先运行全局安装脚本"
    exit 1
fi

# 设置项目环境
export NINJA_PROJECT_DIR="$PROJECT_DIR"
cd "$PROJECT_DIR"

# 运行主程序
exec "$STM32_NINJA_HOME/scripts/ninja_main.sh" "$@"
EOF
    chmod +x "$INSTALL_PATH/scripts/ninja_project.sh"
}

# 创建卸载脚本
create_uninstall_script() {
    cat > "$INSTALL_PATH/uninstall.sh" << EOF
#!/bin/bash
# STM32 Ninja 卸载脚本

echo "卸载 STM32 Ninja..."
echo "安装目录: $INSTALL_PATH"
echo ""
echo -n "确定要卸载吗? (y/N): "
read -n 1 confirm
echo ""

if [ "\$confirm" = "y" ] || [ "\$confirm" = "Y" ]; then
    # 删除安装目录
    rm -rf "$INSTALL_PATH"
    
    # 提示清理环境变量
    echo ""
    echo "请手动从以下文件中删除STM32_NINJA相关的环境变量："
    echo "  - ~/.bashrc"
    echo "  - ~/.bash_profile"
    echo ""
    echo "卸载完成"
else
    echo "取消卸载"
fi
EOF
    chmod +x "$INSTALL_PATH/uninstall.sh"
}

# 显示安装摘要
show_summary() {
    echo ""
    echo "========================================"
    success "安装完成！"
    echo ""
    echo "安装位置: $INSTALL_PATH"
    echo ""
    echo "使用方法："
    echo "  1. 全局命令："
    echo "     stm32ninja init          # 在当前目录初始化项目"
    echo ""
    echo "  2. 在项目目录中："
    echo "     cd /path/to/your/project"
    echo "     stm32ninja init"
    echo ""
    echo "  3. 卸载："
    echo "     $INSTALL_PATH/uninstall.sh"
    echo ""
    echo "注意：请运行以下命令使环境变量生效："
    echo "  source ~/.bashrc"
    echo "========================================"
}

# 主安装流程
main() {
    show_logo
    
    # 检查权限
    check_permissions
    
    # 选择安装路径
    select_install_path
    
    # 确认安装
    if [ -d "$INSTALL_PATH" ]; then
        warn "安装目录已存在: $INSTALL_PATH"
        echo -n "是否覆盖安装? (y/N): "
        read -n 1 confirm
        echo ""
        if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
            error "安装已取消"
            exit 1
        fi
    fi
    
    # 执行安装
    install_core
    create_init_script
    create_project_script
    create_uninstall_script
    setup_environment
    
    # 显示摘要
    show_summary
}

# 运行主函数
main "$@"