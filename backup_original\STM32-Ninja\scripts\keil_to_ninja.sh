#!/bin/bash
# STM32 Ninja - Keil工程智能转换脚本
# 完整解析Keil工程并生成Ninja构建配置

# 加载通用函数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh" || exit 1

# 全局变量
declare -A PROJECT_INFO
declare -a SOURCE_FILES
declare -a INCLUDE_PATHS
declare -a DEFINES

# XML提取函数
xml_extract() {
    local file="$1"
    local tag="$2"
    grep -o "<$tag>[^<]*</$tag>" "$file" 2>/dev/null | sed "s/<$tag>//;s/<\/$tag>//" | head -1
}

# 提取所有匹配的XML标签
xml_extract_all() {
    local file="$1"
    local tag="$2"
    grep -o "<$tag>[^<]*</$tag>" "$file" 2>/dev/null | sed "s/<$tag>//;s/<\/$tag>//"
}

# 解析Keil工程
parse_keil_project() {
    local uvprojx_file="$1"
    local project_dir="$2"
    
    info "解析Keil工程..."
    
    # 提取Device
    local device=$(xml_extract "$uvprojx_file" "Device")
    [[ -z "$device" ]] && device="STM32F407VETx"
    PROJECT_INFO["device"]="$device"
    
    # 提取包含路径（关键改进！）
    info "从Keil工程提取包含路径..."
    # 查找非空的IncludePath标签
    local keil_includes=$(grep "<IncludePath>" "$uvprojx_file" 2>/dev/null | \
                         grep -v "<IncludePath></IncludePath>" | \
                         sed 's/.*<IncludePath>//;s/<\/IncludePath>.*//' | \
                         head -1)
    
    if [[ -n "$keil_includes" ]]; then
        # Keil使用分号分隔路径
        IFS=';' read -ra paths <<< "$keil_includes"
        for path in "${paths[@]}"; do
            path=$(echo "$path" | sed 's/^[ \t]*//;s/[ \t]*$//')  # 去除空格
            if [[ -n "$path" ]]; then
                # 转换为相对路径格式
                if [[ "$path" == ../* ]] || [[ "$path" == ./* ]]; then
                    # 已经是相对路径，直接使用
                    INCLUDE_PATHS+=("-I$path")
                elif [[ "$path" == /* ]]; then
                    # 绝对路径，保持不变
                    INCLUDE_PATHS+=("-I$path")
                else
                    # 其他情况，添加 ../
                    INCLUDE_PATHS+=("-I../$path")
                fi
            fi
        done
        success "从Keil工程提取了 ${#paths[@]} 个包含路径"
    fi
    
    # 提取宏定义
    info "从Keil工程提取宏定义..."
    local keil_defines=$(grep -o "<Define>[^<]*</Define>" "$uvprojx_file" 2>/dev/null | \
                        sed "s/<Define>//;s/<\/Define>//" | \
                        head -1)
    
    if [[ -n "$keil_defines" ]]; then
        # Keil使用逗号分隔定义
        IFS=',' read -ra defs <<< "$keil_defines"
        for def in "${defs[@]}"; do
            def=$(echo "$def" | sed 's/^[ \t]*//;s/[ \t]*$//')  # 去除空格
            if [[ -n "$def" ]]; then
                DEFINES+=("-D$def")
            fi
        done
        success "从Keil工程提取了 ${#defs[@]} 个宏定义"
    fi
    
    # MCU信息
    case "$device" in
        STM32F103*)
            PROJECT_INFO["mcu_series"]="STM32F1xx"
            PROJECT_INFO["cpu_type"]="cortex-m3"
            PROJECT_INFO["fpu"]="none"
            PROJECT_INFO["float_abi"]="soft"
            ;;
        STM32F407*|STM32F405*)
            PROJECT_INFO["mcu_series"]="STM32F4xx"
            PROJECT_INFO["cpu_type"]="cortex-m4"
            PROJECT_INFO["fpu"]="fpv4-sp-d16"
            PROJECT_INFO["float_abi"]="hard"
            ;;
        *)
            PROJECT_INFO["mcu_series"]="STM32F4xx"
            PROJECT_INFO["cpu_type"]="cortex-m4"
            PROJECT_INFO["fpu"]="fpv4-sp-d16"
            PROJECT_INFO["float_abi"]="hard"
            ;;
    esac
    
    success "MCU: $device"
}

# 全面扫描包含路径
scan_include_paths() {
    local project_dir="$1"
    
    info "扫描补充包含路径..."
    
    # 记录已有路径数量
    local existing_paths=${#INCLUDE_PATHS[@]}
    
    # 基础目录（如果还没有添加）
    [[ ! " ${INCLUDE_PATHS[@]} " =~ " -I. " ]] && INCLUDE_PATHS+=("-I.")
    [[ ! " ${INCLUDE_PATHS[@]} " =~ " -I.. " ]] && INCLUDE_PATHS+=("-I..")
    
    # 递归查找所有包含目录
    info "扫描头文件目录..."
    local header_dirs=()
    while IFS= read -r dir; do
        header_dirs+=("$dir")
    done < <(find .. -type f -name "*.h" -exec dirname {} \; 2>/dev/null | sort -u)
    
    # 添加所有找到的目录
    for dir in "${header_dirs[@]}"; do
        local rel_dir="${dir#../}"
        INCLUDE_PATHS+=("-I../$rel_dir")
    done
    
    # 特别添加常见目录（即使没有.h文件）
    local common_dirs=(
        "Core/Inc"
        "Inc"
        "include"
        "App"
        "Application"
        "User"
        "Drivers/STM32${PROJECT_INFO["mcu_series"]}_HAL_Driver/Inc"
        "Drivers/STM32${PROJECT_INFO["mcu_series"]}_HAL_Driver/Inc/Legacy"
        "Drivers/CMSIS/Device/ST/STM32${PROJECT_INFO["mcu_series"]}/Include"
        "Drivers/CMSIS/Include"
        "Middlewares"
        "Components"
    )
    
    for dir in "${common_dirs[@]}"; do
        if [[ -d "../$dir" ]]; then
            INCLUDE_PATHS+=("-I../$dir")
        fi
    done
    
    # 去重并排序
    INCLUDE_PATHS=($(printf "%s\n" "${INCLUDE_PATHS[@]}" | sort -u))
    
    local new_paths=$((${#INCLUDE_PATHS[@]} - existing_paths))
    success "新增 $new_paths 个包含路径（总计 ${#INCLUDE_PATHS[@]} 个）"
}

# 扫描源文件
scan_source_files() {
    local project_dir="$1"
    
    info "扫描源文件..."
    
    # 从uvprojx提取
    local uvprojx_file=""
    for f in *.uvprojx; do
        if [[ -f "$f" ]]; then
            uvprojx_file="$f"
            break
        fi
    done
    
    if [[ -n "$uvprojx_file" ]]; then
        while IFS= read -r line; do
            if [[ "$line" =~ \<FilePath\>([^<]+)\</FilePath\> ]]; then
                local file="${BASH_REMATCH[1]}"
                file="${file//\\//}"
                if [[ "$file" == *.c ]]; then
                    SOURCE_FILES+=("$file")
                fi
            fi
        done < "$uvprojx_file"
    fi
    
    # 如果没找到，扫描目录
    if [[ ${#SOURCE_FILES[@]} -eq 0 ]]; then
        warn "从工程文件未找到源文件，扫描目录..."
        
        # 扫描所有C文件
        while IFS= read -r file; do
            local rel_path="${file#../}"
            SOURCE_FILES+=("../$rel_path")
        done < <(find .. -name "*.c" -type f 2>/dev/null | grep -v "/build/")
    fi
    
    # 去重
    SOURCE_FILES=($(printf "%s\n" "${SOURCE_FILES[@]}" | sort -u))
    
    success "找到 ${#SOURCE_FILES[@]} 个源文件"
}

# 生成build.ninja
generate_ninja_file() {
    local target_name="$1"
    
    info "生成build.ninja..."
    
    # 提取定义
    local uvprojx_file=""
    for f in *.uvprojx; do
        if [[ -f "$f" ]]; then
            uvprojx_file="$f"
            break
        fi
    done
    
    if [[ -n "$uvprojx_file" ]]; then
        # 从Keil工程提取定义
        local defines_str=$(xml_extract "$uvprojx_file" "Define")
        if [[ -n "$defines_str" ]]; then
            IFS=',' read -ra define_array <<< "$defines_str"
            for define in "${define_array[@]}"; do
                define=$(echo "$define" | xargs)
                [[ -n "$define" ]] && DEFINES+=("-D$define")
            done
        fi
    fi
    
    # 添加标准定义
    DEFINES+=("-DUSE_HAL_DRIVER")
    DEFINES+=("-D${PROJECT_INFO["device"]//STM32/STM32}")
    
    # 去重定义
    DEFINES=($(printf "%s\n" "${DEFINES[@]}" | sort -u))
    
    cat > build.ninja << EOF
# Ninja build file for $target_name
# Device: ${PROJECT_INFO["device"]}
# Generated by STM32 Ninja Ultimate

builddir = build
target = $target_name

# Toolchain
cc = arm-none-eabi-gcc
as = arm-none-eabi-gcc
ld = arm-none-eabi-gcc
objcopy = arm-none-eabi-objcopy
size = arm-none-eabi-size

# MCU flags
mcuflags = -mcpu=${PROJECT_INFO["cpu_type"]} -mthumb
EOF

    if [[ "${PROJECT_INFO["fpu"]}" != "none" ]]; then
        echo "mcuflags = \$mcuflags -mfpu=${PROJECT_INFO["fpu"]} -mfloat-abi=${PROJECT_INFO["float_abi"]}" >> build.ninja
    fi
    
    cat >> build.ninja << EOF

# Compile flags
cflags = \$mcuflags -Wall -fdata-sections -ffunction-sections -g -O0
asflags = \$mcuflags -x assembler-with-cpp

# Defines
defines = ${DEFINES[*]}

# Include paths (${#INCLUDE_PATHS[@]} paths)
EOF

    # 写入包含路径
    if [[ ${#INCLUDE_PATHS[@]} -gt 0 ]]; then
        echo -n "includes =" >> build.ninja
        for path in "${INCLUDE_PATHS[@]}"; do
            echo -n " $path" >> build.ninja
        done
        echo "" >> build.ninja
    else
        echo "includes =" >> build.ninja
    fi
    
    cat >> build.ninja << EOF

# Link flags
ldflags = \$mcuflags -specs=nano.specs -specs=nosys.specs
ldflags = \$ldflags -Wl,--gc-sections -Wl,--print-memory-usage

# Build rules
rule cc
  command = \$cc \$cflags \$includes \$defines -MMD -MF \$out.d -c \$in -o \$out
  description = CC \$out
  depfile = \$out.d
  deps = gcc

rule as
  command = \$as \$asflags -c \$in -o \$out
  description = AS \$out

rule link
  command = \$ld \$ldflags -o \$out \$in -lc -lm -lnosys
  description = LINK \$out

rule hex
  command = \$objcopy -O ihex \$in \$out
  description = HEX \$out

rule bin
  command = \$objcopy -O binary -S \$in \$out
  description = BIN \$out

# Source files
EOF

    # 添加源文件
    local obj_files=""
    for src in "${SOURCE_FILES[@]}"; do
        local obj_name="${src//\//_}"
        obj_name="${obj_name//../_}"
        obj_name="${obj_name%.c}.o"
        echo "build \$builddir/$obj_name: cc $src" >> build.ninja
        obj_files="$obj_files \$builddir/$obj_name"
    done
    
    # 查找启动文件
    local startup_found=false
    for pattern in "startup*.s" "../startup*.s" "../**/startup*.s" "*.s" "../*.s"; do
        for file in $pattern; do
            if [[ -f "$file" ]] && [[ "$file" == *startup* ]]; then
                # 检查是否为Keil格式
                if grep -q "AREA\|EXPORT\|DCD\|ENDP\|EQU" "$file" 2>/dev/null; then
                    info "检测到Keil格式启动文件，转换中..."
                    # 检查是否在全局安装环境
                    # 确保生成在当前目录（可能是MDK-ARM子目录）
                    local gnu_startup_file="startup_gnu.s"
                    local convert_script=""
                    
                    # 优先使用 STM32_NINJA_HOME 环境变量
                    if [ -n "$STM32_NINJA_HOME" ] && [ -f "$STM32_NINJA_HOME/scripts/convert_keil_startup.sh" ]; then
                        convert_script="$STM32_NINJA_HOME/scripts/convert_keil_startup.sh"
                    elif [ -n "$NINJA_HOME" ] && [ -f "$NINJA_HOME/scripts/convert_keil_startup.sh" ]; then
                        convert_script="$NINJA_HOME/scripts/convert_keil_startup.sh"
                    elif [ -f "$SCRIPT_DIR/convert_keil_startup.sh" ]; then
                        convert_script="$SCRIPT_DIR/convert_keil_startup.sh"
                    elif [ -f "./scripts/convert_keil_startup.sh" ]; then
                        convert_script="./scripts/convert_keil_startup.sh"
                    fi
                    
                    if [ -n "$convert_script" ]; then
                        "$convert_script" "$file" "$gnu_startup_file"
                    else
                        error "找不到 convert_keil_startup.sh 脚本"
                        error "搜索路径："
                        error "  - STM32_NINJA_HOME: $STM32_NINJA_HOME"
                        error "  - NINJA_HOME: $NINJA_HOME"
                        error "  - SCRIPT_DIR: $SCRIPT_DIR"
                        return 1
                    fi
                    
                    if [ -f "$gnu_startup_file" ]; then
                        echo "" >> build.ninja
                        echo "# Converted startup file" >> build.ninja
                        # 直接使用文件名，ninja会在当前目录查找
                        echo "build \$builddir/startup.o: as $gnu_startup_file" >> build.ninja
                        info "已生成GNU格式启动文件: $gnu_startup_file"
                    else
                        error "启动文件转换失败"
                        return 1
                    fi
                else
                    echo "" >> build.ninja
                    echo "# Startup file" >> build.ninja
                    echo "build \$builddir/startup.o: as $file" >> build.ninja
                fi
                obj_files="$obj_files \$builddir/startup.o"
                startup_found=true
                info "找到启动文件: $file"
                break 2
            fi
        done
    done
    
    # 如果没找到，创建一个
    if [[ "$startup_found" == "false" ]]; then
        create_minimal_startup
        echo "" >> build.ninja
        echo "# Generated startup file" >> build.ninja
        echo "build \$builddir/startup.o: as startup_stm32.s" >> build.ninja
        obj_files="$obj_files \$builddir/startup.o"
    fi
    
    # 查找链接脚本
    local ld_script=""
    for pattern in "*.ld" "../*.ld" "../*/*.ld" "*.lds" "../*.lds"; do
        for file in $pattern; do
            if [[ -f "$file" ]]; then
                ld_script="$file"
                info "找到链接脚本: $ld_script"
                break 2
            fi
        done
    done
    
    if [[ -z "$ld_script" ]]; then
        create_minimal_linker_script
        ld_script="STM32_FLASH.ld"
    fi
    
    echo "" >> build.ninja
    echo "# Linker script" >> build.ninja
    echo "ldflags = \$ldflags -T$ld_script" >> build.ninja
    
    # 链接
    echo "" >> build.ninja
    echo "# Link" >> build.ninja
    echo "build \$builddir/\$target.elf: link$obj_files" >> build.ninja
    
    # 输出
    echo "" >> build.ninja
    echo "# Output files" >> build.ninja
    echo "build \$builddir/\$target.hex: hex \$builddir/\$target.elf" >> build.ninja
    echo "build \$builddir/\$target.bin: bin \$builddir/\$target.elf" >> build.ninja
    
    # 大小信息
    echo "" >> build.ninja
    echo "rule size" >> build.ninja
    echo "  command = \$size \$in" >> build.ninja
    echo "  description = SIZE \$in" >> build.ninja
    echo "" >> build.ninja
    echo "build size: size \$builddir/\$target.elf" >> build.ninja
    echo "  implicit = \$builddir/\$target.hex" >> build.ninja
    
    # 默认目标
    echo "" >> build.ninja
    echo "# Default target" >> build.ninja
    echo "default \$builddir/\$target.hex size" >> build.ninja
    
    # 清理
    echo "" >> build.ninja
    echo "# Clean" >> build.ninja
    echo "rule clean" >> build.ninja
    echo "  command = rm -rf \$builddir *.ld *.s" >> build.ninja
    echo "build clean: clean" >> build.ninja
    
    success "生成build.ninja完成"
    
    # 保存MCU信息供烧录使用
    mkdir -p build
    cat > build/mcu_info.conf << EOF
# STM32 Ninja MCU信息文件
# 由编译过程自动生成，供烧录时使用
MCU_DEVICE="${PROJECT_INFO["device"]}"
MCU_SERIES="${PROJECT_INFO["mcu_series"]}"
MCU_CPU_TYPE="${PROJECT_INFO["cpu_type"]}"
MCU_FPU="${PROJECT_INFO["fpu"]}"
MCU_FLOAT_ABI="${PROJECT_INFO["float_abi"]}"
EOF
    info "保存MCU信息到 build/mcu_info.conf"
}

# 创建最小启动文件
create_minimal_startup() {
    local device="${PROJECT_INFO["device"]}"
    local cpu="${PROJECT_INFO["cpu_type"]:-cortex-m4}"
    local fpu="${PROJECT_INFO["fpu"]:-fpv4-sp-d16}"
    
    info "创建启动文件..."
    cat > startup_stm32.s << EOF
/* Minimal startup for $device */
.syntax unified
.cpu $cpu
.fpu $fpu
.thumb

.global g_pfnVectors
.global Default_Handler

.word _estack
.word Reset_Handler

.section .text.Reset_Handler
.weak Reset_Handler
.type Reset_Handler, %function
Reset_Handler:
    ldr sp, =_estack
    
    /* Copy data */
    ldr r0, =_sdata
    ldr r1, =_edata
    ldr r2, =_sidata
    movs r3, #0
    b LoopCopyDataInit

CopyDataInit:
    ldr r4, [r2, r3]
    str r4, [r0, r3]
    adds r3, r3, #4

LoopCopyDataInit:
    adds r4, r0, r3
    cmp r4, r1
    bcc CopyDataInit
    
    /* Zero bss */
    ldr r2, =_sbss
    ldr r4, =_ebss
    movs r3, #0
    b LoopFillZerobss

FillZerobss:
    str r3, [r2], #4

LoopFillZerobss:
    cmp r2, r4
    bcc FillZerobss

    /* Call main */
    bl main
    bx lr

.size Reset_Handler, .-Reset_Handler

/* Default handler */
.section .text.Default_Handler,"ax",%progbits
Default_Handler:
    b Default_Handler
.size Default_Handler, .-Default_Handler

/* Weak main */
.weak main
.thumb_set main,Default_Handler
EOF
}

# 创建链接脚本
create_minimal_linker_script() {
    info "创建链接脚本..."
    cat > STM32_FLASH.ld << 'EOF'
ENTRY(Reset_Handler)
_estack = ORIGIN(RAM) + LENGTH(RAM);

MEMORY
{
  FLASH (rx)  : ORIGIN = 0x08000000, LENGTH = 512K
  RAM (xrw)   : ORIGIN = 0x20000000, LENGTH = 128K
}

SECTIONS
{
  .text :
  {
    KEEP(*(.isr_vector))
    *(.text*)
    *(.rodata*)
    . = ALIGN(4);
    _etext = .;
  } >FLASH

  _sidata = LOADADDR(.data);

  .data :
  {
    . = ALIGN(4);
    _sdata = .;
    *(.data*)
    . = ALIGN(4);
    _edata = .;
  } >RAM AT> FLASH

  .bss :
  {
    . = ALIGN(4);
    _sbss = .;
    *(.bss*)
    *(COMMON)
    . = ALIGN(4);
    _ebss = .;
  } >RAM

  ._user_heap_stack :
  {
    . = ALIGN(8);
    PROVIDE(end = .);
    . = . + 0x400;
    . = ALIGN(8);
  } >RAM
}
EOF
}

# 显示诊断信息
show_diagnostics() {
    if [[ "$DEBUG" == "1" ]]; then
        echo ""
        echo "诊断信息："
        echo "=========="
        echo "包含路径：${#INCLUDE_PATHS[@]} 个"
        echo "源文件：${#SOURCE_FILES[@]} 个"
    fi
}

# 主函数
main() {
    echo ""
    echo "======================================"
    echo "  STM32 Ninja - Keil智能转换"
    echo "======================================"
    echo ""
    
    # 查找uvprojx
    local uvprojx_file=""
    for f in *.uvprojx; do
        if [[ -f "$f" ]]; then
            uvprojx_file="$f"
            break
        fi
    done
    
    if [[ -z "$uvprojx_file" ]]; then
        error "未找到Keil工程文件"
        exit 1
    fi
    
    local target_name=$(basename "$uvprojx_file" .uvprojx)
    info "工程: $target_name"
    
    # 解析工程
    parse_keil_project "$uvprojx_file" ".."
    
    # 扫描包含路径（这是关键！）
    scan_include_paths ".."
    
    # 扫描源文件
    scan_source_files ".."
    
    # 显示诊断信息
    show_diagnostics
    
    # 生成ninja文件
    generate_ninja_file "$target_name"
    
    # 生成快速编译脚本
    cat > quick_build.sh << 'EOF'
#!/bin/bash
# STM32 Ninja 快速编译脚本

echo "[信息] 开始Ninja高速编译..."

# 计时开始
start_time=$(date +%s%3N)

# 执行编译
NINJA="/d/STM32CubeCLT_1.18.0/Ninja/bin/ninja.exe"
if $NINJA -j $(nproc); then
    # 计时结束
    end_time=$(date +%s%3N)
    duration=$((end_time - start_time))
    
    echo ""
    echo "[成功] 编译完成！用时: ${duration}ms"
    echo ""
    echo "    __  __ ___ ____ _   _ "
    echo "   |  \/  |_ _/ ___| | | |"
    echo "   | |\/| || | |   | | | |"
    echo "   | |  | || | |___| |_| |"
    echo "   |_|  |_|___\____|\___/ "
    echo ""
    echo "   编译任务圆满完成！"
    echo ""
    exit 0
else
    echo ""
    echo "[错误] 编译失败！"
    echo ""
    # 尝试分析错误
    if [ -f build.ninja ]; then
        echo "提示："
        echo "1. 检查是否缺少头文件或宏定义"
        echo "2. 尝试运行 ../scripts/smart_build_v2.sh 进行智能修复"
        echo "3. 检查包含路径是否正确"
    fi
    exit 1
fi
EOF
    chmod +x quick_build.sh
    
    echo ""
    success "转换完成！"
    echo "运行 ./quick_build.sh 开始编译"
}

# 执行
main "$@"