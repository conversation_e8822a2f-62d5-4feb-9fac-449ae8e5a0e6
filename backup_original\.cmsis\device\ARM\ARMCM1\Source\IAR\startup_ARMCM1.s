;/**************************************************************************//**
; * @file     startup_ARMCM1.s
; * @brief    CMSIS Core Device Startup File for
; *           ARMCM1 Device
; * @version  V1.0.0
; * @date     20. July 2018
; ******************************************************************************/
;/*
; * Copyright (c) 2009-2018 Arm Limited. All rights reserved.
; *
; * SPDX-License-Identifier: Apache-2.0
; *
; * Licensed under the Apache License, Version 2.0 (the License); you may
; * not use this file except in compliance with the License.
; * You may obtain a copy of the License at
; *
; * www.apache.org/licenses/LICENSE-2.0
; *
; * Unless required by applicable law or agreed to in writing, software
; * distributed under the License is distributed on an AS IS BASIS, WITHOUT
; * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
; * See the License for the specific language governing permissions and
; * limitations under the License.
; */

;
; The modules in this file are included in the libraries, and may be replaced
; by any user-defined modules that define the PUBLIC symbol _program_start or
; a user defined start symbol.
; To override the cstartup defined in the library, simply add your modified
; version to the workbench project.
;
; The vector table is normally located at address 0.
; When debugging in RAM, it can be located in RAM, aligned to at least 2^6.
; The name "__vector_table" has special meaning for C-SPY:
; it is where the SP start value is found, and the NVIC vector
; table register (VTOR) is initialized to this address if != 0.
;
; Cortex-M version
;

                MODULE   ?cstartup

                ;; Forward declaration of sections.
                SECTION  CSTACK:DATA:NOROOT(3)

                SECTION  .intvec:CODE:NOROOT(2)

                EXTERN   __iar_program_start
                EXTERN   SystemInit
                PUBLIC   __vector_table
                PUBLIC   __vector_table_0x1c
                PUBLIC   __Vectors
                PUBLIC   __Vectors_End
                PUBLIC   __Vectors_Size

                DATA

__vector_table
                DCD      sfe(CSTACK)                         ;     Top of Stack
                DCD      Reset_Handler                       ;     Reset Handler
                DCD      NMI_Handler                         ; -14 NMI Handler
                DCD      HardFault_Handler                   ; -13 Hard Fault Handler
                DCD      0                                   ;     Reserved
                DCD      0                                   ;     Reserved
                DCD      0                                   ;     Reserved
__vector_table_0x1c
                DCD      0                                   ;     Reserved
                DCD      0                                   ;     Reserved
                DCD      0                                   ;     Reserved
                DCD      0                                   ;     Reserved
                DCD      SVC_Handler                         ;  -5 SVCall Handler
                DCD      0                                   ;     Reserved
                DCD      0                                   ;     Reserved
                DCD      PendSV_Handler                      ;  -2 PendSV Handler
                DCD      SysTick_Handler                     ;  -1 SysTick Handler

                ; Interrupts
                DCD      Interrupt0_Handler                  ;   0 Interrupt 0
                DCD      Interrupt1_Handler                  ;   1 Interrupt 1
                DCD      Interrupt2_Handler                  ;   2 Interrupt 2
                DCD      Interrupt3_Handler                  ;   3 Interrupt 3
                DCD      Interrupt4_Handler                  ;   4 Interrupt 4
                DCD      Interrupt5_Handler                  ;   5 Interrupt 5
                DCD      Interrupt6_Handler                  ;   6 Interrupt 6
                DCD      Interrupt7_Handler                  ;   7 Interrupt 7
                DCD      Interrupt8_Handler                  ;   8 Interrupt 8
                DCD      Interrupt9_Handler                  ;   9 Interrupt 9

                DS32    ( 22)                                ; Interrupts 10 .. 31 are left out
__Vectors_End

__Vectors       EQU      __vector_table
__Vectors_Size  EQU      __Vectors_End - __Vectors


                THUMB

; Reset Handler

                PUBWEAK  Reset_Handler
                SECTION  .text:CODE:REORDER:NOROOT(2)
Reset_Handler
                LDR      R0, =SystemInit
                BLX      R0
                LDR      R0, =__iar_program_start
                BX       R0


                PUBWEAK NMI_Handler
                PUBWEAK HardFault_Handler
                PUBWEAK SVC_Handler
                PUBWEAK PendSV_Handler
                PUBWEAK SysTick_Handler

                PUBWEAK Interrupt0_Handler
                PUBWEAK Interrupt1_Handler
                PUBWEAK Interrupt2_Handler
                PUBWEAK Interrupt3_Handler
                PUBWEAK Interrupt4_Handler
                PUBWEAK Interrupt5_Handler
                PUBWEAK Interrupt6_Handler
                PUBWEAK Interrupt7_Handler
                PUBWEAK Interrupt8_Handler
                PUBWEAK Interrupt9_Handler
                SECTION .text:CODE:REORDER:NOROOT(1)
NMI_Handler
HardFault_Handler
SVC_Handler
PendSV_Handler
SysTick_Handler

Interrupt0_Handler
Interrupt1_Handler
Interrupt2_Handler
Interrupt3_Handler
Interrupt4_Handler
Interrupt5_Handler
Interrupt6_Handler
Interrupt7_Handler
Interrupt8_Handler
Interrupt9_Handler
Default_Handler
                B        .


                END
