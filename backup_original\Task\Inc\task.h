#ifndef __TASK_H__
#define __TASK_H__

#include "main.h"
#include "stdio.h"
#include "fine_line.h"
#include "pid.h"

// 全局变量声明
extern unsigned char Do_count;  // 用于任务状态切换
extern int res;                 // 通用变量
extern float pitch;             // 姿态角 pitch
extern float roll;              // 姿态角 roll
extern float yaw;               // 姿态角 yaw
extern int16_t gx, gy, gz;      // MPU6050陀螺仪数据
extern int16_t ax, ay, az;      // MPU6050加速度数据

// 函数声明
void task_0(void);  // 任务0：初始状态
void task_1(void);  // 任务1：逐步执行任务逻辑
void task_2(void);  // 任务2：线检测与运动控制
void task_3(void);  // 任务3：新增任务
void task_4(void);  // 任务4：循环执行三次task_3
void UpdateAndDisplayYaw(void);  // 辅助函数：获取并显示yaw角度
void delay_ms(uint32_t ms);  // 延时函数

#endif // __TASK_H__
