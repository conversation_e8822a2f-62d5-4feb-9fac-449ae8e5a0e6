<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [Ide To Keil\Ide To Keil.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image Ide To Keil\Ide To Keil.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Wed Jul 23 16:19:32 2025
<BR><P>
<H3>Maximum Stack Usage =        544 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
__rt_entry_main &rArr; main &rArr; task_4 &rArr; ControlYawMovement &rArr; MPU6050_DMP_Get_Data &rArr; dmp_read_fifo &rArr; mpu_read_fifo_stream &rArr; mpu_reset_fifo &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[55]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[20]">ADC1_2_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[20]">ADC1_2_IRQHandler</a><BR>
 <LI><a href="#[8]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[8]">BusFault_Handler</a><BR>
 <LI><a href="#[6]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[6]">HardFault_Handler</a><BR>
 <LI><a href="#[7]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[7]">MemManage_Handler</a><BR>
 <LI><a href="#[5]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">NMI_Handler</a><BR>
 <LI><a href="#[9]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[9]">UsageFault_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[20]">ADC1_2_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[8]">BusFault_Handler</a> from stm32f1xx_it.o(i.BusFault_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[23]">CAN1_RX1_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[24]">CAN1_SCE_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[19]">DMA1_Channel1_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[1a]">DMA1_Channel2_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[1b]">DMA1_Channel3_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[1c]">DMA1_Channel4_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[1d]">DMA1_Channel5_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[1e]">DMA1_Channel6_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[1f]">DMA1_Channel7_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[b]">DebugMon_Handler</a> from stm32f1xx_it.o(i.DebugMon_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[14]">EXTI0_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[36]">EXTI15_10_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[15]">EXTI1_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[16]">EXTI2_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[17]">EXTI3_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[18]">EXTI4_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[25]">EXTI9_5_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[12]">FLASH_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[6]">HardFault_Handler</a> from stm32f1xx_it.o(i.HardFault_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[2e]">I2C1_ER_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[2d]">I2C1_EV_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[30]">I2C2_ER_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[2f]">I2C2_EV_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[7]">MemManage_Handler</a> from stm32f1xx_it.o(i.MemManage_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[5]">NMI_Handler</a> from stm32f1xx_it.o(i.NMI_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[f]">PVD_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[c]">PendSV_Handler</a> from stm32f1xx_it.o(i.PendSV_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[13]">RCC_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[37]">RTC_Alarm_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[11]">RTC_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[4]">Reset_Handler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[31]">SPI1_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[32]">SPI2_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[a]">SVC_Handler</a> from stm32f1xx_it.o(i.SVC_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[d]">SysTick_Handler</a> from stm32f1xx_it.o(i.SysTick_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[39]">SystemInit</a> from system_stm32f1xx.o(i.SystemInit) referenced from startup_stm32f103xb.o(.text)
 <LI><a href="#[10]">TAMPER_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[26]">TIM1_BRK_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[29]">TIM1_CC_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[28]">TIM1_TRG_COM_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[27]">TIM1_UP_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[2a]">TIM2_IRQHandler</a> from stm32f1xx_it.o(i.TIM2_IRQHandler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[2b]">TIM3_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[2c]">TIM4_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[33]">USART1_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[34]">USART2_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[35]">USART3_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[38]">USBWakeUp_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[21]">USB_HP_CAN1_TX_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[22]">USB_LP_CAN1_RX0_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[9]">UsageFault_Handler</a> from stm32f1xx_it.o(i.UsageFault_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[e]">WWDG_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[3b]">__main</a> from __main.o(!!!main) referenced from startup_stm32f103xb.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[3b]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[3c]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[3e]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[134]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[135]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[3f]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[136]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[43]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[137]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[138]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[139]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[13a]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[13b]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[13c]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[13d]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000002))

<P><STRONG><a name="[13e]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[13f]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[140]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[141]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[142]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[143]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[144]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[145]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[146]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[147]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[148]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[149]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[14a]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[14b]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[48]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[14c]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[14d]"></a>__rt_lib_shutdown_fini_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[14e]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000009))

<P><STRONG><a name="[14f]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000011))

<P><STRONG><a name="[150]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000012))

<P><STRONG><a name="[151]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[152]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000006))

<P><STRONG><a name="[153]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E))

<P><STRONG><a name="[3d]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[154]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[40]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[42]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[155]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[44]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 544 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; task_4 &rArr; ControlYawMovement &rArr; MPU6050_DMP_Get_Data &rArr; dmp_read_fifo &rArr; mpu_read_fifo_stream &rArr; mpu_reset_fifo &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[156]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[56]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[47]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[157]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[49]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[4]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>ADC1_2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>TAMPER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM1_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM1_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM1_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>USBWakeUp_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>USB_HP_CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>USB_LP_CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32f103xb.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[f9]"></a>__aeabi_ldivmod</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llsdiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_ldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_st_biases
</UL>

<P><STRONG><a name="[4b]"></a>_ll_sdiv</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, llsdiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[10c]"></a>memcmp</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, memcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_load_firmware
</UL>

<P><STRONG><a name="[4d]"></a>__aeabi_memset</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, aeabi_memset.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_lp_quat
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_6x_lp_quat
</UL>

<P><STRONG><a name="[bf]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM2_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[158]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[159]"></a>__rt_memclr_w</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[53]"></a>_memset_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memclr
</UL>

<P><STRONG><a name="[15a]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[15b]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[15c]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[4c]"></a>__aeabi_uldivmod</STRONG> (Thumb, 0 bytes, Stack size 48 bytes, lludivv7m.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_sdiv
</UL>

<P><STRONG><a name="[15d]"></a>_ll_udiv</STRONG> (Thumb, 238 bytes, Stack size 48 bytes, lludivv7m.o(.text), UNUSED)

<P><STRONG><a name="[4f]"></a>__read_errno</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, _rserrno.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>

<P><STRONG><a name="[51]"></a>__set_errno</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, _rserrno.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __set_errno
</UL>
<BR>[Calls]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
</UL>

<P><STRONG><a name="[15e]"></a>__aeabi_memclr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)

<P><STRONG><a name="[52]"></a>__rt_memclr</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset_w
</UL>

<P><STRONG><a name="[4e]"></a>_memset</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[50]"></a>__aeabi_errno_addr</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__read_errno
</UL>

<P><STRONG><a name="[15f]"></a>__errno$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[160]"></a>__rt_errno_addr$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[161]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[54]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[162]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[41]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[46]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[4a]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[163]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[164]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[8]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[165]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[57]"></a>ControlYawMovement</STRONG> (Thumb, 166 bytes, Stack size 104 bytes, direction.o(i.ControlYawMovement))
<BR><BR>[Stack]<UL><LI>Max Depth = 512<LI>Call Chain = ControlYawMovement &rArr; MPU6050_DMP_Get_Data &rArr; dmp_read_fifo &rArr; mpu_read_fifo_stream &rArr; mpu_reset_fifo &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfrcmple
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSpeedR
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSpeedL
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_realize
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_4
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_3
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_2
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_1
</UL>

<P><STRONG><a name="[b]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[bd]"></a>Error_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, main.o(i.Error_Handler))
<BR><BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM2_Init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
</UL>

<P><STRONG><a name="[60]"></a>Find_Line_Begins</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, fine_line.o(i.Find_Line_Begins))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Find_Line_Begins &rArr; OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;line_detect
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_LedFind_Scan
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSpeedR
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSpeedL
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_3
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_2
</UL>

<P><STRONG><a name="[64]"></a>HAL_Delay</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, stm32f1xx_hal.o(i.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_4
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_3
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_2
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_1
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_0
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Scan
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sensors
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_bypass
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_st_biases
</UL>

<P><STRONG><a name="[71]"></a>HAL_GPIO_Init</STRONG> (Thumb, 446 bytes, Stack size 40 bytes, stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_MspPostInit
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_MspInit
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Init
</UL>

<P><STRONG><a name="[9f]"></a>HAL_GPIO_ReadPin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Scan
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;line_detect
</UL>

<P><STRONG><a name="[ff]"></a>HAL_GPIO_TogglePin</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin))
<BR><BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_4
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[bb]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin))
<BR><BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_4
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_3
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_2
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_1
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SDA
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SCL
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSpeedR
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSpeedL
</UL>

<P><STRONG><a name="[65]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f1xx_hal.o(i.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnRXNEFlagUntilTimeout
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_read_fifo
</UL>

<P><STRONG><a name="[66]"></a>HAL_I2C_Init</STRONG> (Thumb, 376 bytes, Stack size 16 bytes, stm32f1xx_hal_i2c.o(i.HAL_I2C_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
</UL>

<P><STRONG><a name="[69]"></a>HAL_I2C_Mem_Read</STRONG> (Thumb, 574 bytes, Stack size 64 bytes, stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnRXNEFlagUntilTimeout
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryRead
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_bypass
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_read_mem
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_read_fifo_stream
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_st_biases
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_accel_prod_shift
</UL>

<P><STRONG><a name="[6d]"></a>HAL_I2C_Mem_Write</STRONG> (Thumb, 294 bytes, Stack size 64 bytes, stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sensors
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sample_rate
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_lpf
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_int_latched
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_gyro_fsr
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_bypass
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_accel_fsr
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_read_mem
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_lp_accel_mode
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_load_firmware
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_int_enable
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_st_biases
</UL>

<P><STRONG><a name="[67]"></a>HAL_I2C_MspInit</STRONG> (Thumb, 82 bytes, Stack size 32 bytes, i2c.o(i.HAL_I2C_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
</UL>

<P><STRONG><a name="[d0]"></a>HAL_IncTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f1xx_hal.o(i.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[72]"></a>HAL_Init</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f1xx_hal.o(i.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[74]"></a>HAL_InitTick</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, stm32f1xx_hal.o(i.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[75]"></a>HAL_MspInit</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, stm32f1xx_hal_msp.o(i.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[7f]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
</UL>

<P><STRONG><a name="[77]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[73]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[79]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 280 bytes, Stack size 32 bytes, stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[68]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq))
<BR><BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
</UL>

<P><STRONG><a name="[7a]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq))
<BR><BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[7b]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 778 bytes, Stack size 40 bytes, stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[76]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_SYSTICK_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[8e]"></a>HAL_TIMEx_BreakCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[90]"></a>HAL_TIMEx_CommutCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[c0]"></a>HAL_TIMEx_MasterConfigSynchronization</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_TIMEx_MasterConfigSynchronization
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM2_Init
</UL>

<P><STRONG><a name="[7c]"></a>HAL_TIM_Base_Init</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM2_Init
</UL>

<P><STRONG><a name="[7d]"></a>HAL_TIM_Base_MspInit</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, tim.o(i.HAL_TIM_Base_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[fc]"></a>HAL_TIM_Base_Start_IT</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT))
<BR><BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[80]"></a>HAL_TIM_ConfigClockSource</STRONG> (Thumb, 220 bytes, Stack size 16 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_TIM_ConfigClockSource &rArr; TIM_TI2_ConfigInputStage
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI2_ConfigInputStage
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI1_ConfigInputStage
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITRx_SetConfig
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETR_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM2_Init
</UL>

<P><STRONG><a name="[85]"></a>HAL_TIM_Encoder_Init</STRONG> (Thumb, 164 bytes, Stack size 24 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_TIM_Encoder_Init &rArr; HAL_TIM_Encoder_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_MspInit
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
</UL>

<P><STRONG><a name="[86]"></a>HAL_TIM_Encoder_MspInit</STRONG> (Thumb, 72 bytes, Stack size 24 bytes, tim.o(i.HAL_TIM_Encoder_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_TIM_Encoder_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_Init
</UL>

<P><STRONG><a name="[87]"></a>HAL_TIM_Encoder_Start</STRONG> (Thumb, 142 bytes, Stack size 16 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_TIM_Encoder_Start &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CCxChannelCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8a]"></a>HAL_TIM_IC_CaptureCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[89]"></a>HAL_TIM_IRQHandler</STRONG> (Thumb, 358 bytes, Stack size 16 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_TIM_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_TriggerCallback
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PeriodElapsedCallback
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_PulseFinishedCallback
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_OC_DelayElapsedCallback
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_CaptureCallback
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_CommutCallback
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_BreakCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_IRQHandler
</UL>

<P><STRONG><a name="[91]"></a>HAL_TIM_MspPostInit</STRONG> (Thumb, 62 bytes, Stack size 24 bytes, tim.o(i.HAL_TIM_MspPostInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_TIM_MspPostInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM2_Init
</UL>

<P><STRONG><a name="[8b]"></a>HAL_TIM_OC_DelayElapsedCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[92]"></a>HAL_TIM_PWM_ConfigChannel</STRONG> (Thumb, 204 bytes, Stack size 16 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_TIM_PWM_ConfigChannel &rArr; TIM_OC4_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4_SetConfig
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3_SetConfig
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1_SetConfig
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM2_Init
</UL>

<P><STRONG><a name="[97]"></a>HAL_TIM_PWM_Init</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_TIM_PWM_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_MspInit
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM2_Init
</UL>

<P><STRONG><a name="[98]"></a>HAL_TIM_PWM_MspInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit))
<BR><BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
</UL>

<P><STRONG><a name="[8c]"></a>HAL_TIM_PWM_PulseFinishedCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[99]"></a>HAL_TIM_PWM_Start</STRONG> (Thumb, 144 bytes, Stack size 8 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_TIM_PWM_Start &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CCxChannelCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8d]"></a>HAL_TIM_PeriodElapsedCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[8f]"></a>HAL_TIM_TriggerCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[6]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[100]"></a>KEY_GetTaskIndex</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, key.o(i.KEY_GetTaskIndex))
<BR><BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9c]"></a>KEY_Init</STRONG> (Thumb, 52 bytes, Stack size 24 bytes, key.o(i.KEY_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = KEY_Init &rArr; KEY_UpdateOLED &rArr; OLED_ShowNum &rArr; OLED_ShowChar &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_UpdateOLED
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fd]"></a>KEY_IsSelectionMode</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, key.o(i.KEY_IsSelectionMode))
<BR><BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fe]"></a>KEY_IsTaskExecuted</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, key.o(i.KEY_IsTaskExecuted))
<BR><BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9e]"></a>KEY_Scan</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, key.o(i.KEY_Scan))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = KEY_Scan &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a0]"></a>KEY_SetTaskExecuted</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, key.o(i.KEY_SetTaskExecuted))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = KEY_SetTaskExecuted &rArr; KEY_UpdateOLED &rArr; OLED_ShowNum &rArr; OLED_ShowChar &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_UpdateOLED
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a1]"></a>KEY_SwitchTask</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, key.o(i.KEY_SwitchTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = KEY_SwitchTask &rArr; KEY_UpdateOLED &rArr; OLED_ShowNum &rArr; OLED_ShowChar &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_UpdateOLED
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9d]"></a>KEY_UpdateOLED</STRONG> (Thumb, 196 bytes, Stack size 16 bytes, key.o(i.KEY_UpdateOLED))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = KEY_UpdateOLED &rArr; OLED_ShowNum &rArr; OLED_ShowChar &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowNum
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_UpdateSelectionTimer
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_SwitchTask
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_SetTaskExecuted
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Init
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a4]"></a>KEY_UpdateSelectionTimer</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, key.o(i.KEY_UpdateSelectionTimer))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = KEY_UpdateSelectionTimer &rArr; KEY_UpdateOLED &rArr; OLED_ShowNum &rArr; OLED_ShowChar &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_UpdateOLED
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[58]"></a>MPU6050_DMP_Get_Data</STRONG> (Thumb, 460 bytes, Stack size 104 bytes, mpu6050.o(i.MPU6050_DMP_Get_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = MPU6050_DMP_Get_Data &rArr; dmp_read_fifo &rArr; mpu_read_fifo_stream &rArr; mpu_reset_fifo &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbnf
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_read_fifo
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_2
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ControlYawMovement
</UL>

<P><STRONG><a name="[ae]"></a>MPU6050_DMP_Init</STRONG> (Thumb, 172 bytes, Stack size 16 bytes, mpu6050.o(i.MPU6050_DMP_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 456 + Unknown Stack Size
<LI>Call Chain = MPU6050_DMP_Init &rArr; run_self_test &rArr; mpu_run_self_test &rArr; mpu_set_dmp_state &rArr; mpu_set_sample_rate &rArr; mpu_lp_accel_mode &rArr; mpu_configure_fifo &rArr; mpu_reset_fifo &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inv_row_2_scale
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_orientation
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_fifo_rate
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_load_motion_driver_firmware
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sensors
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sample_rate
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_configure_fifo
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_4
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_3
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_2
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_1
</UL>

<P><STRONG><a name="[ba]"></a>MX_GPIO_Init</STRONG> (Thumb, 216 bytes, Stack size 48 bytes, gpio.o(i.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Init
</UL>

<P><STRONG><a name="[bc]"></a>MX_I2C2_Init</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, i2c.o(i.MX_I2C2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = MX_I2C2_Init &rArr; HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[be]"></a>MX_TIM2_Init</STRONG> (Thumb, 162 bytes, Stack size 64 bytes, tim.o(i.MX_TIM2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = MX_TIM2_Init &rArr; HAL_TIM_MspPostInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_MspPostInit
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c1]"></a>MX_TIM4_Init</STRONG> (Thumb, 100 bytes, Stack size 56 bytes, tim.o(i.MX_TIM4_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = MX_TIM4_Init &rArr; HAL_TIM_Encoder_Init &rArr; HAL_TIM_Encoder_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_Init
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.NMI_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[a2]"></a>OLED_Clear</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, oled.o(i.OLED_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = OLED_Clear &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteData
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SetCursor
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_4
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_2
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_UpdateOLED
</UL>

<P><STRONG><a name="[c4]"></a>OLED_I2C_Init</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, oled.o(i.OLED_I2C_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = OLED_I2C_Init &rArr; MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SDA
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SCL
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[c7]"></a>OLED_I2C_SendByte</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, oled.o(i.OLED_I2C_SendByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SDA
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SCL
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteData
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
</UL>

<P><STRONG><a name="[c8]"></a>OLED_I2C_Start</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, oled.o(i.OLED_I2C_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = OLED_I2C_Start
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SDA
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SCL
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteData
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
</UL>

<P><STRONG><a name="[c9]"></a>OLED_I2C_Stop</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, oled.o(i.OLED_I2C_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = OLED_I2C_Stop
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SDA
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SCL
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteData
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
</UL>

<P><STRONG><a name="[ca]"></a>OLED_Init</STRONG> (Thumb, 172 bytes, Stack size 8 bytes, oled.o(i.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = OLED_Init &rArr; OLED_I2C_Init &rArr; MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Init
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[cd]"></a>OLED_Pow</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, oled.o(i.OLED_Pow))
<BR><BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowSignedNum
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowNum
</UL>

<P><STRONG><a name="[c2]"></a>OLED_SetCursor</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, oled.o(i.OLED_SetCursor))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>

<P><STRONG><a name="[cc]"></a>OLED_ShowChar</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, oled.o(i.OLED_ShowChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = OLED_ShowChar &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteData
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SetCursor
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowSignedNum
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowNum
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
</UL>

<P><STRONG><a name="[a3]"></a>OLED_ShowNum</STRONG> (Thumb, 66 bytes, Stack size 32 bytes, oled.o(i.OLED_ShowNum))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = OLED_ShowNum &rArr; OLED_ShowChar &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Pow
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_4
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_2
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_UpdateOLED
</UL>

<P><STRONG><a name="[ce]"></a>OLED_ShowSignedNum</STRONG> (Thumb, 88 bytes, Stack size 32 bytes, oled.o(i.OLED_ShowSignedNum))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = OLED_ShowSignedNum &rArr; OLED_ShowChar &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Pow
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_1
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_0
</UL>

<P><STRONG><a name="[63]"></a>OLED_ShowString</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, oled.o(i.OLED_ShowString))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_4
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_3
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_2
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_UpdateOLED
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Find_Line_Begins
</UL>

<P><STRONG><a name="[c5]"></a>OLED_W_SCL</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, oled.o(i.OLED_W_SCL))
<BR><BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Stop
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Start
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_SendByte
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Init
</UL>

<P><STRONG><a name="[c6]"></a>OLED_W_SDA</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, oled.o(i.OLED_W_SDA))
<BR><BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Stop
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Start
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_SendByte
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Init
</UL>

<P><STRONG><a name="[cb]"></a>OLED_WriteCommand</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, oled.o(i.OLED_WriteCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = OLED_WriteCommand &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Stop
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Start
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_SendByte
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SetCursor
</UL>

<P><STRONG><a name="[c3]"></a>OLED_WriteData</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, oled.o(i.OLED_WriteData))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = OLED_WriteData &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Stop
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Start
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_SendByte
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>

<P><STRONG><a name="[fb]"></a>PID_init</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, pid.o(i.PID_init))
<BR><BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[59]"></a>PID_realize</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, pid.o(i.PID_realize))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = PID_realize &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ControlYawMovement
</UL>

<P><STRONG><a name="[c]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>SetSpeedL</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, motor.o(i.SetSpeedL))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SetSpeedL
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;abds
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_4
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_3
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_2
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_1
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_0
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Find_Line_Begins
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ControlYawMovement
</UL>

<P><STRONG><a name="[5e]"></a>SetSpeedR</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, motor.o(i.SetSpeedR))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SetSpeedR
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;abds
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_4
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_3
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_2
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_1
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_0
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Find_Line_Begins
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ControlYawMovement
</UL>

<P><STRONG><a name="[d]"></a>SysTick_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.SysTick_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[d1]"></a>SystemClock_Config</STRONG> (Thumb, 94 bytes, Stack size 72 bytes, main.o(i.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[39]"></a>SystemInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, system_stm32f1xx.o(i.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(.text)
</UL>
<P><STRONG><a name="[2a]"></a>TIM2_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.TIM2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM2_IRQHandler &rArr; HAL_TIM_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[7e]"></a>TIM_Base_SetConfig</STRONG> (Thumb, 94 bytes, Stack size 20 bytes, stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[88]"></a>TIM_CCxChannelCmd</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_CCxChannelCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Start
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_Start
</UL>

<P><STRONG><a name="[81]"></a>TIM_ETR_SetConfig</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_ETR_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[94]"></a>TIM_OC2_SetConfig</STRONG> (Thumb, 84 bytes, Stack size 12 bytes, stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_OC2_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[9]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[dd]"></a>__ARM_fpclassify</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
</UL>

<P><STRONG><a name="[d2]"></a>__kernel_poly</STRONG> (Thumb, 170 bytes, Stack size 24 bytes, poly.o(i.__kernel_poly))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
</UL>

<P><STRONG><a name="[d4]"></a>__mathlib_dbl_infnan</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dunder.o(i.__mathlib_dbl_infnan))
<BR><BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
</UL>

<P><STRONG><a name="[d6]"></a>__mathlib_dbl_infnan2</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, dunder.o(i.__mathlib_dbl_infnan2))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __mathlib_dbl_infnan2 &rArr; __aeabi_dadd
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
</UL>

<P><STRONG><a name="[d7]"></a>__mathlib_dbl_invalid</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dunder.o(i.__mathlib_dbl_invalid))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __mathlib_dbl_invalid &rArr; __aeabi_ddiv
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
</UL>

<P><STRONG><a name="[d9]"></a>__mathlib_dbl_underflow</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, dunder.o(i.__mathlib_dbl_underflow))
<BR><BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
</UL>

<P><STRONG><a name="[aa]"></a>asin</STRONG> (Thumb, 572 bytes, Stack size 56 bytes, asin.o(i.asin))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = asin &rArr; sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
</UL>

<P><STRONG><a name="[e1]"></a>atan</STRONG> (Thumb, 474 bytes, Stack size 40 bytes, atan.o(i.atan))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = atan &rArr; __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
</UL>

<P><STRONG><a name="[ad]"></a>atan2</STRONG> (Thumb, 374 bytes, Stack size 32 bytes, atan2.o(i.atan2))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = atan2 &rArr; atan &rArr; __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmpeq
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
</UL>
<BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
</UL>

<P><STRONG><a name="[e3]"></a>dmp_enable_6x_lp_quat</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat))
<BR><BR>[Stack]<UL><LI>Max Depth = 176 + Unknown Stack Size
<LI>Call Chain = dmp_enable_6x_lp_quat &rArr; mpu_write_mem &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[b6]"></a>dmp_enable_feature</STRONG> (Thumb, 464 bytes, Stack size 24 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature))
<BR><BR>[Stack]<UL><LI>Max Depth = 224 + Unknown Stack Size
<LI>Call Chain = dmp_enable_feature &rArr; dmp_set_tap_thresh &rArr; mpu_write_mem &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_time_multi
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_time
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_count
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_axes
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_shake_reject_timeout
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_shake_reject_time
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_shake_reject_thresh
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_lp_quat
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_gyro_cal
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_6x_lp_quat
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[e6]"></a>dmp_enable_gyro_cal</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = dmp_enable_gyro_cal &rArr; mpu_write_mem &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[ef]"></a>dmp_enable_lp_quat</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat))
<BR><BR>[Stack]<UL><LI>Max Depth = 176 + Unknown Stack Size
<LI>Call Chain = dmp_enable_lp_quat &rArr; mpu_write_mem &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[b3]"></a>dmp_load_motion_driver_firmware</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = dmp_load_motion_driver_firmware &rArr; mpu_load_firmware &rArr; mpu_read_mem &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_load_firmware
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[a5]"></a>dmp_read_fifo</STRONG> (Thumb, 354 bytes, Stack size 88 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = dmp_read_fifo &rArr; mpu_read_fifo_stream &rArr; mpu_reset_fifo &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_read_fifo_stream
</UL>
<BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
</UL>

<P><STRONG><a name="[f2]"></a>dmp_set_accel_bias</STRONG> (Thumb, 212 bytes, Stack size 40 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = dmp_set_accel_bias &rArr; mpu_write_mem &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_accel_sens
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
</UL>

<P><STRONG><a name="[b7]"></a>dmp_set_fifo_rate</STRONG> (Thumb, 82 bytes, Stack size 32 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = dmp_set_fifo_rate &rArr; mpu_write_mem &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[f4]"></a>dmp_set_gyro_bias</STRONG> (Thumb, 196 bytes, Stack size 24 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = dmp_set_gyro_bias &rArr; mpu_write_mem &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
</UL>

<P><STRONG><a name="[b5]"></a>dmp_set_orientation</STRONG> (Thumb, 246 bytes, Stack size 40 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = dmp_set_orientation &rArr; mpu_write_mem &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[ec]"></a>dmp_set_shake_reject_thresh</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = dmp_set_shake_reject_thresh &rArr; mpu_write_mem &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[ed]"></a>dmp_set_shake_reject_time</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = dmp_set_shake_reject_time &rArr; mpu_write_mem &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[ee]"></a>dmp_set_shake_reject_timeout</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = dmp_set_shake_reject_timeout &rArr; mpu_write_mem &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[e8]"></a>dmp_set_tap_axes</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = dmp_set_tap_axes &rArr; mpu_write_mem &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[e9]"></a>dmp_set_tap_count</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = dmp_set_tap_count &rArr; mpu_write_mem &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[e7]"></a>dmp_set_tap_thresh</STRONG> (Thumb, 274 bytes, Stack size 32 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = dmp_set_tap_thresh &rArr; mpu_write_mem &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbnf
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2uiz
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_accel_fsr
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[ea]"></a>dmp_set_tap_time</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = dmp_set_tap_time &rArr; mpu_write_mem &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[eb]"></a>dmp_set_tap_time_multi</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = dmp_set_tap_time_multi &rArr; mpu_write_mem &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[62]"></a>get_LedFind_Scan</STRONG> (Thumb, 110 bytes, Stack size 20 bytes, fine_line.o(i.get_LedFind_Scan))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = get_LedFind_Scan
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_3
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_2
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_1
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Find_Line_Begins
</UL>

<P><STRONG><a name="[61]"></a>line_detect</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, fine_line.o(i.line_detect))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = line_detect
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_3
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_2
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_1
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Find_Line_Begins
</UL>

<P><STRONG><a name="[45]"></a>main</STRONG> (Thumb, 188 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 544 + Unknown Stack Size
<LI>Call Chain = main &rArr; task_4 &rArr; ControlYawMovement &rArr; MPU6050_DMP_Get_Data &rArr; dmp_read_fifo &rArr; mpu_read_fifo_stream &rArr; mpu_reset_fifo &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_4
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_3
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_2
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_1
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_0
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_init
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM2_Init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_UpdateSelectionTimer
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_UpdateOLED
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_SwitchTask
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_SetTaskExecuted
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Scan
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_IsTaskExecuted
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_IsSelectionMode
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Init
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_GetTaskIndex
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Start
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_Start
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start_IT
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_TogglePin
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[b1]"></a>mpu_configure_fifo</STRONG> (Thumb, 78 bytes, Stack size 24 bytes, inv_mpu.o(i.mpu_configure_fifo))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = mpu_configure_fifo &rArr; mpu_reset_fifo &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_int_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_lp_accel_mode
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[f6]"></a>mpu_get_accel_fsr</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, inv_mpu.o(i.mpu_get_accel_fsr))
<BR><BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[f3]"></a>mpu_get_accel_sens</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, inv_mpu.o(i.mpu_get_accel_sens))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_accel_bias
</UL>

<P><STRONG><a name="[110]"></a>mpu_get_gyro_fsr</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, inv_mpu.o(i.mpu_get_gyro_fsr))
<BR><BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[113]"></a>mpu_get_gyro_sens</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, inv_mpu.o(i.mpu_get_gyro_sens))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
</UL>

<P><STRONG><a name="[111]"></a>mpu_get_lpf</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, inv_mpu.o(i.mpu_get_lpf))
<BR><BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[112]"></a>mpu_get_sample_rate</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, inv_mpu.o(i.mpu_get_sample_rate))
<BR><BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[af]"></a>mpu_init</STRONG> (Thumb, 334 bytes, Stack size 48 bytes, inv_mpu.o(i.mpu_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = mpu_init &rArr; mpu_set_sample_rate &rArr; mpu_lp_accel_mode &rArr; mpu_configure_fifo &rArr; mpu_reset_fifo &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sensors
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sample_rate
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_lpf
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_gyro_fsr
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_bypass
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_accel_fsr
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_configure_fifo
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[f0]"></a>mpu_load_firmware</STRONG> (Thumb, 178 bytes, Stack size 88 bytes, inv_mpu.o(i.mpu_load_firmware))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = mpu_load_firmware &rArr; mpu_read_mem &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_read_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_load_motion_driver_firmware
</UL>

<P><STRONG><a name="[10d]"></a>mpu_lp_accel_mode</STRONG> (Thumb, 220 bytes, Stack size 56 bytes, inv_mpu.o(i.mpu_lp_accel_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = mpu_lp_accel_mode &rArr; mpu_configure_fifo &rArr; mpu_reset_fifo &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_lpf
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_int_latched
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_configure_fifo
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sample_rate
</UL>

<P><STRONG><a name="[f1]"></a>mpu_read_fifo_stream</STRONG> (Thumb, 188 bytes, Stack size 48 bytes, inv_mpu.o(i.mpu_read_fifo_stream))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = mpu_read_fifo_stream &rArr; mpu_reset_fifo &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_read_fifo
</UL>

<P><STRONG><a name="[10b]"></a>mpu_read_mem</STRONG> (Thumb, 106 bytes, Stack size 40 bytes, inv_mpu.o(i.mpu_read_mem))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = mpu_read_mem &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_load_firmware
</UL>

<P><STRONG><a name="[e5]"></a>mpu_reset_fifo</STRONG> (Thumb, 454 bytes, Stack size 40 bytes, inv_mpu.o(i.mpu_reset_fifo))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = mpu_reset_fifo &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_read_fifo
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_lp_quat
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_6x_lp_quat
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_read_fifo_stream
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_configure_fifo
</UL>

<P><STRONG><a name="[10f]"></a>mpu_run_self_test</STRONG> (Thumb, 234 bytes, Stack size 72 bytes, inv_mpu.o(i.mpu_run_self_test))
<BR><BR>[Stack]<UL><LI>Max Depth = 400<LI>Call Chain = mpu_run_self_test &rArr; mpu_set_dmp_state &rArr; mpu_set_sample_rate &rArr; mpu_lp_accel_mode &rArr; mpu_configure_fifo &rArr; mpu_reset_fifo &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sensors
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sample_rate
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_lpf
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_gyro_fsr
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_accel_fsr
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_sample_rate
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_lpf
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_gyro_fsr
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_accel_fsr
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_configure_fifo
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_st_biases
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
</UL>

<P><STRONG><a name="[108]"></a>mpu_set_accel_fsr</STRONG> (Thumb, 116 bytes, Stack size 32 bytes, inv_mpu.o(i.mpu_set_accel_fsr))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = mpu_set_accel_fsr &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
</UL>

<P><STRONG><a name="[10a]"></a>mpu_set_bypass</STRONG> (Thumb, 320 bytes, Stack size 48 bytes, inv_mpu.o(i.mpu_set_bypass))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = mpu_set_bypass &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
</UL>

<P><STRONG><a name="[b9]"></a>mpu_set_dmp_state</STRONG> (Thumb, 140 bytes, Stack size 40 bytes, inv_mpu.o(i.mpu_set_dmp_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = mpu_set_dmp_state &rArr; mpu_set_sample_rate &rArr; mpu_lp_accel_mode &rArr; mpu_configure_fifo &rArr; mpu_reset_fifo &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sample_rate
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_bypass
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_int_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[107]"></a>mpu_set_gyro_fsr</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, inv_mpu.o(i.mpu_set_gyro_fsr))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = mpu_set_gyro_fsr &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
</UL>

<P><STRONG><a name="[10e]"></a>mpu_set_int_latched</STRONG> (Thumb, 110 bytes, Stack size 32 bytes, inv_mpu.o(i.mpu_set_int_latched))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = mpu_set_int_latched &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sensors
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_lp_accel_mode
</UL>

<P><STRONG><a name="[109]"></a>mpu_set_lpf</STRONG> (Thumb, 116 bytes, Stack size 32 bytes, inv_mpu.o(i.mpu_set_lpf))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = mpu_set_lpf &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sample_rate
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_lp_accel_mode
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
</UL>

<P><STRONG><a name="[b2]"></a>mpu_set_sample_rate</STRONG> (Thumb, 142 bytes, Stack size 40 bytes, inv_mpu.o(i.mpu_set_sample_rate))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = mpu_set_sample_rate &rArr; mpu_lp_accel_mode &rArr; mpu_configure_fifo &rArr; mpu_reset_fifo &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_lpf
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_lp_accel_mode
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[b0]"></a>mpu_set_sensors</STRONG> (Thumb, 214 bytes, Stack size 48 bytes, inv_mpu.o(i.mpu_set_sensors))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = mpu_set_sensors &rArr; mpu_set_int_latched &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_int_latched
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[e4]"></a>mpu_write_mem</STRONG> (Thumb, 106 bytes, Stack size 40 bytes, inv_mpu.o(i.mpu_write_mem))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = mpu_write_mem &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_time_multi
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_time
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_count
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_axes
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_shake_reject_timeout
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_shake_reject_time
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_shake_reject_thresh
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_orientation
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_gyro_bias
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_fifo_rate
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_accel_bias
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_lp_quat
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_gyro_cal
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_6x_lp_quat
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_load_firmware
</UL>

<P><STRONG><a name="[df]"></a>sqrt</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, sqrt.o(i.sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
</UL>

<P><STRONG><a name="[101]"></a>task_0</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, task.o(i.task_0))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = task_0 &rArr; OLED_ShowSignedNum &rArr; OLED_ShowChar &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowSignedNum
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSpeedR
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSpeedL
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[102]"></a>task_1</STRONG> (Thumb, 318 bytes, Stack size 0 bytes, task.o(i.task_1))
<BR><BR>[Stack]<UL><LI>Max Depth = 512 + Unknown Stack Size
<LI>Call Chain = task_1 &rArr; ControlYawMovement &rArr; MPU6050_DMP_Get_Data &rArr; dmp_read_fifo &rArr; mpu_read_fifo_stream &rArr; mpu_reset_fifo &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowSignedNum
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;line_detect
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_LedFind_Scan
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSpeedR
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSpeedL
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ControlYawMovement
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[103]"></a>task_2</STRONG> (Thumb, 740 bytes, Stack size 24 bytes, task.o(i.task_2))
<BR><BR>[Stack]<UL><LI>Max Depth = 536 + Unknown Stack Size
<LI>Call Chain = task_2 &rArr; ControlYawMovement &rArr; MPU6050_DMP_Get_Data &rArr; dmp_read_fifo &rArr; mpu_read_fifo_stream &rArr; mpu_reset_fifo &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowNum
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;line_detect
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_LedFind_Scan
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Find_Line_Begins
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSpeedR
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSpeedL
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ControlYawMovement
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[104]"></a>task_3</STRONG> (Thumb, 468 bytes, Stack size 0 bytes, task.o(i.task_3))
<BR><BR>[Stack]<UL><LI>Max Depth = 512 + Unknown Stack Size
<LI>Call Chain = task_3 &rArr; ControlYawMovement &rArr; MPU6050_DMP_Get_Data &rArr; dmp_read_fifo &rArr; mpu_read_fifo_stream &rArr; mpu_reset_fifo &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;line_detect
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_LedFind_Scan
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Find_Line_Begins
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSpeedR
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSpeedL
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ControlYawMovement
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[105]"></a>task_4</STRONG> (Thumb, 334 bytes, Stack size 32 bytes, task.o(i.task_4))
<BR><BR>[Stack]<UL><LI>Max Depth = 544 + Unknown Stack Size
<LI>Call Chain = task_4 &rArr; ControlYawMovement &rArr; MPU6050_DMP_Get_Data &rArr; dmp_read_fifo &rArr; mpu_read_fifo_stream &rArr; mpu_reset_fifo &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_TogglePin
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowNum
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSpeedR
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSpeedL
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ControlYawMovement
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ac]"></a>__aeabi_d2f</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, d2f.o(x$fpl$d2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
</UL>

<P><STRONG><a name="[115]"></a>_d2f</STRONG> (Thumb, 98 bytes, Stack size 32 bytes, d2f.o(x$fpl$d2f), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[d3]"></a>__aeabi_dadd</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, daddsub_clz.o(x$fpl$dadd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
</UL>

<P><STRONG><a name="[118]"></a>_dadd</STRONG> (Thumb, 332 bytes, Stack size 16 bytes, daddsub_clz.o(x$fpl$dadd), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub1
</UL>

<P><STRONG><a name="[11b]"></a>__fpl_dcheck_NaN1</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, dcheck1.o(x$fpl$dcheck1))
<BR><BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>

<P><STRONG><a name="[11f]"></a>__fpl_dcmp_Inf</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, dcmpi.o(x$fpl$dcmpinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmpeq
</UL>

<P><STRONG><a name="[d8]"></a>__aeabi_ddiv</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, ddiv.o(x$fpl$ddiv))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
</UL>

<P><STRONG><a name="[11d]"></a>_ddiv</STRONG> (Thumb, 552 bytes, Stack size 32 bytes, ddiv.o(x$fpl$ddiv), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[e2]"></a>__aeabi_cdcmpeq</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, deqf.o(x$fpl$deqf))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_cdcmpeq
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
</UL>

<P><STRONG><a name="[11e]"></a>_dcmpeq</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, deqf.o(x$fpl$deqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcmp_Inf
</UL>

<P><STRONG><a name="[ab]"></a>__aeabi_dmul</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
</UL>

<P><STRONG><a name="[120]"></a>_dmul</STRONG> (Thumb, 332 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[117]"></a>__fpl_dnaninf</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, dnaninf.o(x$fpl$dnaninf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __fpl_dnaninf
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmpeq
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ddiv
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2f
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
</UL>

<P><STRONG><a name="[11a]"></a>__fpl_dretinf</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dretinf.o(x$fpl$dretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ddiv
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
</UL>

<P><STRONG><a name="[de]"></a>__aeabi_drsub</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, daddsub_clz.o(x$fpl$drsb))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_drsub
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
</UL>

<P><STRONG><a name="[121]"></a>_drsb</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, daddsub_clz.o(x$fpl$drsb), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub1
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd1
</UL>

<P><STRONG><a name="[114]"></a>_dsqrt</STRONG> (Thumb, 456 bytes, Stack size 24 bytes, dsqrt_noumaal.o(x$fpl$dsqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
</UL>

<P><STRONG><a name="[e0]"></a>__aeabi_dsub</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, daddsub_clz.o(x$fpl$dsub))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_dsub
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
</UL>

<P><STRONG><a name="[123]"></a>_dsub</STRONG> (Thumb, 464 bytes, Stack size 32 bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd1
</UL>

<P><STRONG><a name="[a9]"></a>__aeabi_f2d</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
</UL>

<P><STRONG><a name="[124]"></a>_f2d</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
</UL>

<P><STRONG><a name="[5a]"></a>__aeabi_fadd</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, faddsub_clz.o(x$fpl$fadd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_realize
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ControlYawMovement
</UL>

<P><STRONG><a name="[126]"></a>_fadd</STRONG> (Thumb, 196 bytes, Stack size 16 bytes, faddsub_clz.o(x$fpl$fadd), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub1
</UL>

<P><STRONG><a name="[128]"></a>__fpl_fcheck_NaN1</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, fcheck1.o(x$fpl$fcheck1))
<BR><BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbnf
</UL>

<P><STRONG><a name="[12d]"></a>__fpl_fcmp_Inf</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, fcmpi.o(x$fpl$fcmpinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmple
</UL>

<P><STRONG><a name="[dc]"></a>__aeabi_fdiv</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, fdiv.o(x$fpl$fdiv))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
</UL>

<P><STRONG><a name="[129]"></a>_fdiv</STRONG> (Thumb, 384 bytes, Stack size 16 bytes, fdiv.o(x$fpl$fdiv), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[5d]"></a>__aeabi_f2iz</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, ffix.o(x$fpl$ffix))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_2
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_1
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ControlYawMovement
</UL>

<P><STRONG><a name="[12a]"></a>_ffix</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, ffix.o(x$fpl$ffix), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[f7]"></a>__aeabi_f2uiz</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, ffixu.o(x$fpl$ffixu))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2uiz
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
</UL>

<P><STRONG><a name="[12b]"></a>_ffixu</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, ffixu.o(x$fpl$ffixu), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[a6]"></a>__aeabi_i2f</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, fflt_clz.o(x$fpl$fflt))
<BR><BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_2
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
</UL>

<P><STRONG><a name="[166]"></a>_fflt</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, fflt_clz.o(x$fpl$fflt), UNUSED)

<P><STRONG><a name="[f5]"></a>__aeabi_ui2f</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, fflt_clz.o(x$fpl$ffltu))
<BR><BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
</UL>

<P><STRONG><a name="[167]"></a>_ffltu</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, fflt_clz.o(x$fpl$ffltu), UNUSED)

<P><STRONG><a name="[168]"></a>__aeabi_cfcmple</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, fleqf.o(x$fpl$fleqf), UNUSED)

<P><STRONG><a name="[12c]"></a>_fcmple</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, fleqf.o(x$fpl$fleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_Inf
</UL>

<P><STRONG><a name="[130]"></a>__fpl_fcmple_InfNaN</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fleqf.o(x$fpl$fleqf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_frcmple
</UL>

<P><STRONG><a name="[a8]"></a>__aeabi_fmul</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, fmul.o(x$fpl$fmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_accel_prod_shift
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_realize
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
</UL>

<P><STRONG><a name="[12e]"></a>_fmul</STRONG> (Thumb, 258 bytes, Stack size 16 bytes, fmul.o(x$fpl$fmul), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[125]"></a>__fpl_fnaninf</STRONG> (Thumb, 140 bytes, Stack size 8 bytes, fnaninf.o(x$fpl$fnaninf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmple
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fmul
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ffixu
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ffix
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fdiv
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
</UL>

<P><STRONG><a name="[116]"></a>__fpl_fretinf</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fretinf.o(x$fpl$fretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fmul
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fdiv
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2f
</UL>

<P><STRONG><a name="[5c]"></a>__aeabi_cfrcmple</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, frleqf.o(x$fpl$frleqf))
<BR><BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ControlYawMovement
</UL>

<P><STRONG><a name="[12f]"></a>_frcmple</STRONG> (Thumb, 98 bytes, Stack size 0 bytes, frleqf.o(x$fpl$frleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmple_InfNaN
</UL>

<P><STRONG><a name="[5b]"></a>__aeabi_fsub</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, faddsub_clz.o(x$fpl$fsub))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fsub
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_realize
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ControlYawMovement
</UL>

<P><STRONG><a name="[131]"></a>_fsub</STRONG> (Thumb, 234 bytes, Stack size 16 bytes, faddsub_clz.o(x$fpl$fsub), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd1
</UL>

<P><STRONG><a name="[11c]"></a>__fpl_return_NaN</STRONG> (Thumb, 100 bytes, Stack size 0 bytes, retnan.o(x$fpl$retnan))
<BR><BR>[Calls]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_cmpreturn
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcheck_NaN1
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcheck_NaN1
</UL>

<P><STRONG><a name="[d5]"></a>__ARM_scalbn</STRONG> (Thumb, 92 bytes, Stack size 0 bytes, scalbn.o(x$fpl$scalbn))
<BR><BR>[Calls]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcheck_NaN1
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>

<P><STRONG><a name="[a7]"></a>__ARM_scalbnf</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, scalbnf.o(x$fpl$scalbnf))
<BR><BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcheck_NaN1
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
</UL>

<P><STRONG><a name="[133]"></a>__fpl_cmpreturn</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, trapv.o(x$fpl$trapveneer))
<BR><BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[9b]"></a>I2C_IsAcknowledgeFailed</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
</UL>

<P><STRONG><a name="[6b]"></a>I2C_RequestMemoryRead</STRONG> (Thumb, 246 bytes, Stack size 40 bytes, stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = I2C_RequestMemoryRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
</UL>

<P><STRONG><a name="[6e]"></a>I2C_RequestMemoryWrite</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>

<P><STRONG><a name="[70]"></a>I2C_WaitOnBTFFlagUntilTimeout</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_WaitOnBTFFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_IsAcknowledgeFailed
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>

<P><STRONG><a name="[6a]"></a>I2C_WaitOnFlagUntilTimeout</STRONG> (Thumb, 144 bytes, Stack size 32 bytes, stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryRead
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
</UL>

<P><STRONG><a name="[9a]"></a>I2C_WaitOnMasterAddressFlagUntilTimeout</STRONG> (Thumb, 188 bytes, Stack size 32 bytes, stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryRead
</UL>

<P><STRONG><a name="[6c]"></a>I2C_WaitOnRXNEFlagUntilTimeout</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = I2C_WaitOnRXNEFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
</UL>

<P><STRONG><a name="[6f]"></a>I2C_WaitOnTXEFlagUntilTimeout</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_WaitOnTXEFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_IsAcknowledgeFailed
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryRead
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>

<P><STRONG><a name="[78]"></a>__NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>

<P><STRONG><a name="[84]"></a>TIM_ITRx_SetConfig</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[93]"></a>TIM_OC1_SetConfig</STRONG> (Thumb, 74 bytes, Stack size 12 bytes, stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_OC1_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[95]"></a>TIM_OC3_SetConfig</STRONG> (Thumb, 82 bytes, Stack size 12 bytes, stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_OC3_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[96]"></a>TIM_OC4_SetConfig</STRONG> (Thumb, 64 bytes, Stack size 12 bytes, stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_OC4_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[82]"></a>TIM_TI1_ConfigInputStage</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_TI1_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[83]"></a>TIM_TI2_ConfigInputStage</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_TI2_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[cf]"></a>abds</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, motor.o(i.abds))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSpeedR
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSpeedL
</UL>

<P><STRONG><a name="[da]"></a>accel_self_test</STRONG> (Thumb, 114 bytes, Stack size 48 bytes, inv_mpu.o(i.accel_self_test))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = accel_self_test &rArr; get_accel_prod_shift &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbnf
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_accel_prod_shift
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[db]"></a>get_accel_prod_shift</STRONG> (Thumb, 174 bytes, Stack size 48 bytes, inv_mpu.o(i.get_accel_prod_shift))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = get_accel_prod_shift &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
</UL>

<P><STRONG><a name="[f8]"></a>get_st_biases</STRONG> (Thumb, 1086 bytes, Stack size 64 bytes, inv_mpu.o(i.get_st_biases))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = get_st_biases &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[fa]"></a>gyro_self_test</STRONG> (Thumb, 202 bytes, Stack size 56 bytes, inv_mpu.o(i.gyro_self_test))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = gyro_self_test &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbnf
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[106]"></a>set_int_enable</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, inv_mpu.o(i.set_int_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = set_int_enable &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_configure_fifo
</UL>

<P><STRONG><a name="[b4]"></a>inv_row_2_scale</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, mpu6050.o(i.inv_row_2_scale))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[b8]"></a>run_self_test</STRONG> (Thumb, 130 bytes, Stack size 40 bytes, mpu6050.o(i.run_self_test))
<BR><BR>[Stack]<UL><LI>Max Depth = 440<LI>Call Chain = run_self_test &rArr; mpu_run_self_test &rArr; mpu_set_dmp_state &rArr; mpu_set_sample_rate &rArr; mpu_lp_accel_mode &rArr; mpu_configure_fifo &rArr; mpu_reset_fifo &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_gyro_bias
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_accel_bias
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_gyro_sens
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_accel_sens
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[132]"></a>_fadd1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, faddsub_clz.o(x$fpl$fadd), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub
</UL>

<P><STRONG><a name="[127]"></a>_fsub1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, faddsub_clz.o(x$fpl$fsub), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd
</UL>

<P><STRONG><a name="[122]"></a>_dadd1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, daddsub_clz.o(x$fpl$dadd), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drsb
</UL>

<P><STRONG><a name="[119]"></a>_dsub1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drsb
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
