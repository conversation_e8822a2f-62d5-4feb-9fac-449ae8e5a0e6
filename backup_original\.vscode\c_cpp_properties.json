{"configurations": [{"name": "Ide To Keil", "includePath": ["c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Core\\Inc", "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Drivers\\STM32F1xx_HAL_Driver\\Inc", "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Drivers\\STM32F1xx_HAL_Driver\\Inc\\Legacy", "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Drivers\\CMSIS\\Device\\ST\\STM32F1xx\\Include", "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Drivers\\CMSIS\\Include", "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM\\Task\\Src", "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM\\Task\\Inc", "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM\\Task\\Mpu6050Dmp", "C:\\Keil_v5\\ARM\\ARMCC\\include", "C:\\Keil_v5\\ARM\\ARMCC\\include\\rw", "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\MDK-ARM", "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Core\\Src", "c:\\Users\\<USER>\\Desktop\\Ide To Keil\\Drivers\\STM32F1xx_HAL_Driver\\Src"], "defines": ["USE_HAL_DRIVER", "STM32F103xB", "__CC_ARM", "__arm__", "__align(x)=", "__ALIGNOF__(x)=", "__alignof__(x)=", "__asm(x)=", "__forceinline=", "__restrict=", "__global_reg(n)=", "__inline=", "__int64=long long", "__INTADDR__(expr)=0", "__irq=", "__packed=", "__pure=", "__smc(n)=", "__svc(n)=", "__svc_indirect(n)=", "__svc_indirect_r7(n)=", "__value_in_regs=", "__weak=", "__writeonly=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__register=", "__breakpoint(x)=", "__cdp(x,y,z)=", "__clrex()=", "__clz(x)=0U", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__dmb(x)=", "__dsb(x)=", "__enable_fiq()=", "__enable_irq()=", "__fabs(x)=0.0", "__fabsf(x)=0.0f", "__force_loads()=", "__force_stores()=", "__isb(x)=", "__ldrex(x)=0U", "__ldrexd(x)=0U", "__ldrt(x)=0U", "__memory_changed()=", "__nop()=", "__pld(...)=", "__pli(...)=", "__qadd(x,y)=0", "__qdbl(x)=0", "__qsub(x,y)=0", "__rbit(x)=0U", "__rev(x)=0U", "__return_address()=0U", "__ror(x,y)=0U", "__schedule_barrier()=", "__semihost(x,y)=0", "__sev()=", "__sqrt(x)=0.0", "__sqrtf(x)=0.0f", "__ssat(x,y)=0", "__strex(x,y)=0U", "__strexd(x,y)=0", "__strt(x,y)=", "__swp(x,y)=0U", "__usat(x,y)=0U", "__wfe()=", "__wfi()=", "__yield()=", "__vfp_status(x,y)=0"], "intelliSenseMode": "${default}"}], "version": 4}