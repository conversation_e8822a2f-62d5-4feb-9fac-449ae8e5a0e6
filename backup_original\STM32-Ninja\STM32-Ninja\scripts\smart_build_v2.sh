#!/bin/bash
# STM32 Ninja - 智能构建脚本 V2
# 自动检测并修复构建问题

# 加载通用函数
source "$(dirname "$0")/common.sh"

# 配置
BUILD_DIR="build"
NINJA_BUILD="build.ninja"

# 检查并转换Keil启动文件
check_and_convert_startup() {
    info "检查启动文件..."
    
    for file in $(find . -name "startup*.s" -o -name "startup*.S" 2>/dev/null); do
        if grep -q "AREA\|EXPORT\|DCD\|ENDP\|EQU" "$file" 2>/dev/null; then
            info "发现Keil格式启动文件: $file"
            local basename=$(basename "$file" .s)
            local gnu_file="${basename}_gnu.s"
            
            # 转换启动文件
            ./scripts/convert_keil_startup.sh "$file" "$gnu_file"
            
            # 更新build.ninja
            if [ -f "$NINJA_BUILD" ]; then
                sed -i "s|$file|$gnu_file|g" "$NINJA_BUILD"
                info "更新build.ninja使用转换后的启动文件"
            fi
        fi
    done
}

# 检查并添加缺失的包含路径
check_missing_includes() {
    info "检查缺失的包含路径..."
    
    # 查找所有头文件目录
    local all_includes=""
    for dir in $(find . .. -type d \( -name "Inc" -o -name "inc" -o -name "Include" -o -name "include" -o -name "Headers" \) 2>/dev/null); do
        all_includes="$all_includes -I$dir"
    done
    
    # 查找Components目录下的所有子目录
    for dir in $(find . .. -type d -path "*/Components/*" 2>/dev/null); do
        all_includes="$all_includes -I$dir"
    done
    
    # 特别查找easy_button相关目录
    for dir in $(find . .. -type d -name "*ebtn*" -o -name "*button*" 2>/dev/null); do
        all_includes="$all_includes -I$dir"
    done
    
    # 更新build.ninja
    if [ -f "$NINJA_BUILD" ] && [ -n "$all_includes" ]; then
        # 备份原文件
        cp "$NINJA_BUILD" "${NINJA_BUILD}.bak"
        
        # 在includes行添加新的包含路径
        if grep -q "^includes =" "$NINJA_BUILD"; then
            local current_includes=$(grep "^includes =" "$NINJA_BUILD" | sed 's/^includes = //')
            local new_includes="$current_includes $all_includes"
            # 去重
            new_includes=$(echo "$new_includes" | tr ' ' '\n' | sort -u | tr '\n' ' ')
            sed -i "s|^includes =.*|includes = $new_includes|" "$NINJA_BUILD"
            info "添加额外的包含路径"
        fi
    fi
}

# 智能修复编译错误
fix_compile_errors() {
    local error_log="$1"
    
    if [ ! -f "$error_log" ]; then
        return
    fi
    
    # 修复未定义的宏
    if grep -q "undeclared\|undefined" "$error_log"; then
        info "检测到未定义的宏，尝试修复..."
        
        # 查找可能包含宏定义的文件
        local macro=$(grep -o "[A-Z_]*CFG[A-Z_]*" "$error_log" | head -1)
        if [ -n "$macro" ]; then
            local def_file=$(grep -r "$macro" . --include="*.h" 2>/dev/null | head -1 | cut -d: -f1)
            if [ -n "$def_file" ]; then
                local def_dir=$(dirname "$def_file")
                info "找到宏定义在: $def_file"
                
                # 添加到includes
                if [ -f "$NINJA_BUILD" ]; then
                    sed -i "s|^includes =|includes = -I$def_dir |" "$NINJA_BUILD"
                fi
            fi
        fi
    fi
}

# 主构建函数
build_project() {
    step "开始智能构建..."
    
    # 1. 检查build.ninja是否存在
    if [ ! -f "$NINJA_BUILD" ]; then
        error "未找到build.ninja，请先运行keil_to_ninja.sh"
        return 1
    fi
    
    # 2. 检查并转换启动文件
    check_and_convert_startup
    
    # 3. 检查并添加缺失的包含路径
    check_missing_includes
    
    # 4. 创建构建目录
    mkdir -p "$BUILD_DIR"
    
    # 5. 尝试构建
    step "执行ninja构建..."
    if ninja -v 2>&1 | tee build_output.log; then
        success "构建成功！"
        
        # 显示输出文件信息
        echo ""
        info "输出文件:"
        for file in $BUILD_DIR/*.elf $BUILD_DIR/*.hex $BUILD_DIR/*.bin; do
            if [ -f "$file" ]; then
                echo "  - $file ($(stat -c%s "$file" 2>/dev/null || echo "?") bytes)"
            fi
        done
        
        return 0
    else
        error "构建失败，尝试自动修复..."
        
        # 6. 分析错误并尝试修复
        fix_compile_errors "build_output.log"
        
        # 7. 重新尝试构建
        step "重新构建..."
        if ninja -v; then
            success "构建成功（经过自动修复）！"
            return 0
        else
            error "构建失败，请检查错误信息"
            return 1
        fi
    fi
}

# 清理函数
clean_build() {
    info "清理构建文件..."
    rm -rf "$BUILD_DIR"
    rm -f *.ld *.s *_gnu.s
    rm -f build_output.log
    success "清理完成"
}

# 主程序
main() {
    case "${1:-build}" in
        build)
            build_project
            ;;
        clean)
            clean_build
            ;;
        rebuild)
            clean_build
            build_project
            ;;
        *)
            echo "用法: $0 [build|clean|rebuild]"
            exit 1
            ;;
    esac
}

main "$@"