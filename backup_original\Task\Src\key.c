/* Includes ------------------------------------------------------------------*/
#include "key.h"
#include "gpio.h"
#include "OLED.h"

/* 私有变量 ------------------------------------------------------------------*/
static uint8_t g_taskIndex = 0; // 当前任务索引，默认为任务0
static uint8_t g_isSelectionMode = 1; // 1=选择模式，0=执行模式
static uint32_t g_selectionTimer = 0; // 选择模式计时器
static uint8_t g_taskExecuted = 0; // 任务是否已执行标志

#define SELECTION_TIMEOUT 50 // 5秒选择时间（50 * 100ms）

/**
  * @brief  按键初始化
  * @param  无
  * @retval 无
  */
void KEY_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    /* 使能GPIOB时钟 */
    KEY_CLK_ENABLE;
    
    /* 配置按键引脚为输入模式，上拉 */
    GPIO_InitStruct.Pin = KEY_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(KEY_PORT, &GPIO_InitStruct);
    
    /* 初始化OLED显示 */
    KEY_UpdateOLED();
}

/**
  * @brief  按键扫描
  * @param  mode: 0-不支持连续按，1-支持连续按
  * @retval 按键状态
  */
KeyState KEY_Scan(uint8_t mode)
{
    static uint8_t key_up = 1; // 按键按松开标志
    static uint32_t long_press_count = 0; // 长按计数器
    
    if(mode)
        key_up = 1; // 支持连按
    
    if(key_up && HAL_GPIO_ReadPin(KEY_PORT, KEY_PIN) == GPIO_PIN_RESET)
    {
        HAL_Delay(10); // 消抖
        if(HAL_GPIO_ReadPin(KEY_PORT, KEY_PIN) == GPIO_PIN_RESET)
        {
            key_up = 0;
            long_press_count = 0;
            return KEY_DOWN;
        }
    }
    else if(HAL_GPIO_ReadPin(KEY_PORT, KEY_PIN) == GPIO_PIN_RESET)
    {
        long_press_count++;
        if(long_press_count > 200) // 约2秒长按
        {
            long_press_count = 0;
            return KEY_LONG_PRESS;
        }
    }
    else if(HAL_GPIO_ReadPin(KEY_PORT, KEY_PIN) == GPIO_PIN_SET)
    {
        key_up = 1;
        long_press_count = 0;
    }
    
    return KEY_UP;
}

/**
  * @brief  获取当前任务索引
  * @param  无
  * @retval 当前任务索引
  */
uint8_t KEY_GetTaskIndex(void)
{
    return g_taskIndex;
}

/**
  * @brief  切换到下一个任务
  * @param  无
  * @retval 无
  */
void KEY_SwitchTask(void)
{
    if(g_isSelectionMode) // 只有在选择模式下才能切换
    {
        g_taskIndex = (g_taskIndex + 1) % TASK_COUNT; // 在0到TASK_COUNT-1之间循环
        g_selectionTimer = 0; // 重置计时器
        KEY_UpdateOLED();
    }
}

/**
  * @brief  更新OLED显示
  * @param  无
  * @retval 无
  */
void KEY_UpdateOLED(void)
{
    OLED_Clear();

    if(g_isSelectionMode)
    {
        // 选择模式显示
        OLED_ShowString(1, 1, "SELECT MODE");
        OLED_ShowString(2, 1, "Task:");
        OLED_ShowNum(2, 6, g_taskIndex, 1);

        // 显示剩余时间
        uint8_t remaining = (SELECTION_TIMEOUT - g_selectionTimer) / 10;
        OLED_ShowString(2, 9, "Time:");
        OLED_ShowNum(2, 14, remaining, 1);

        // 显示任务描述
        switch(g_taskIndex)
        {
            case 0:
                OLED_ShowString(3, 1, "Basic Motor");
                break;
            case 1:
                OLED_ShowString(3, 1, "PID Control");
                break;
            case 2:
                OLED_ShowString(3, 1, "Balance Ctrl");
                break;
            case 3:
                OLED_ShowString(3, 1, "New Function");
                break;
            case 4:
                OLED_ShowString(3, 1, "3x Task3 Loop");
                break;
        }
        OLED_ShowString(4, 1, "Press to switch");
    }
    else
    {
        // 执行模式显示 - 只在任务完成后显示，执行期间让task自己控制OLED
        if(g_taskExecuted)
        {
            OLED_Clear();
            OLED_ShowString(1, 1, "TASK");
            OLED_ShowNum(1, 6, g_taskIndex, 1);
            OLED_ShowString(1, 8, "COMPLETED!");
            OLED_ShowString(3, 1, "System Stopped");
            OLED_ShowString(4, 1, "Press Reset");
        }
        // 如果任务正在执行，不更新OLED，让task函数自己控制显示
    }
}

/**
  * @brief  检查是否在选择模式
  * @param  无
  * @retval 1=选择模式，0=执行模式
  */
uint8_t KEY_IsSelectionMode(void)
{
    return g_isSelectionMode;
}

/**
  * @brief  更新选择模式计时器
  * @param  无
  * @retval 无
  */
void KEY_UpdateSelectionTimer(void)
{
    if(g_isSelectionMode)
    {
        g_selectionTimer++;
        if(g_selectionTimer >= SELECTION_TIMEOUT)
        {
            // 选择时间到，进入执行模式
            g_isSelectionMode = 0;
            g_taskExecuted = 0;
            KEY_UpdateOLED();
        }
        else if(g_selectionTimer % 10 == 0) // 每秒更新一次显示
        {
            KEY_UpdateOLED();
        }
    }
}

/**
  * @brief  检查任务是否已执行
  * @param  无
  * @retval 1=已执行，0=未执行
  */
uint8_t KEY_IsTaskExecuted(void)
{
    return g_taskExecuted;
}

/**
  * @brief  设置任务已执行标志
  * @param  无
  * @retval 无
  */
void KEY_SetTaskExecuted(void)
{
    g_taskExecuted = 1;
    KEY_UpdateOLED();
}