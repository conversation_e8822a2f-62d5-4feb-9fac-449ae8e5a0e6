#!/bin/bash
# STM32 Ninja - 检查工具脚本

# 加载通用函数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh" || exit 1

# 检查单个工具
check_tool() {
    local name="$1"
    local cmd="$2"
    local version_flag="${3:---version}"
    
    printf "  %-20s" "$name:"
    
    if command -v "$cmd" &>/dev/null; then
        local version=$($cmd $version_flag 2>&1 | head -1)
        echo -e "${GREEN}✓${NC} $version"
        return 0
    else
        echo -e "${RED}✗${NC} 未找到"
        return 1
    fi
}

# 主程序
main() {
    local all_ok=true
    
    echo "检查必需工具："
    echo ""
    
    # 检查GCC工具链
    setup_toolchain &>/dev/null
    check_tool "ARM GCC" "arm-none-eabi-gcc" || all_ok=false
    check_tool "ARM G++" "arm-none-eabi-g++" || all_ok=false
    check_tool "ARM GDB" "arm-none-eabi-gdb" || all_ok=false
    check_tool "ARM objcopy" "arm-none-eabi-objcopy" || all_ok=false
    check_tool "ARM size" "arm-none-eabi-size" || all_ok=false
    
    echo ""
    echo "检查构建工具："
    echo ""
    
    # 检查CMake
    check_tool "CMake" "cmake" || all_ok=false
    
    # 检查Ninja
    local ninja=$(find_ninja)
    if [ -n "$ninja" ]; then
        check_tool "Ninja" "$ninja" || all_ok=false
    else
        printf "  %-20s" "Ninja:"
        echo -e "${RED}✗${NC} 未找到"
        all_ok=false
    fi
    
    # 只使用Ninja，不再需要Make
    
    echo ""
    echo "检查烧录工具："
    echo ""
    
    # 检查OpenOCD
    local openocd=$(find_openocd)
    if [ -n "$openocd" ]; then
        check_tool "OpenOCD" "$openocd" || all_ok=false
    else
        printf "  %-20s" "OpenOCD:"
        echo -e "${RED}✗${NC} 未找到"
        all_ok=false
    fi
    
    echo ""
    echo "检查可选工具："
    echo ""
    
    # 检查可选工具
    check_tool "Git" "git" || true
    check_tool "Python" "python3" || check_tool "Python" "python" || true
    check_tool "ccache" "ccache" || true
    
    echo ""
    if [ "$all_ok" = true ]; then
        success "所有必需工具已安装"
    else
        warn "部分工具未找到，某些功能可能无法使用"
        echo ""
        echo "安装建议："
        echo "  • STM32CubeCLT: https://www.st.com/en/development-tools/stm32cubeclt.html"
        echo "  • MSYS2: https://www.msys2.org/"
        echo "  • 在MSYS2中安装: pacman -S mingw-w64-x86_64-openocd mingw-w64-x86_64-ninja"
    fi
}

# 执行主程序
main