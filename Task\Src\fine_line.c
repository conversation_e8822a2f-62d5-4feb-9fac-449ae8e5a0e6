#include "main.h"
#include "stdio.h"
#include "oled.h"
#include "Motor.h"

typedef struct {
    GPIO_TypeDef* GPIOx;
    uint16_t GPIO_Pin;
    uint8_t value;
} lineIDdef;

lineIDdef linepin_list[] = {
    {GPIOA, GPIO_PIN_9,  0},
    {GPIOA, GPIO_PIN_10, 0},
    {GPIOA, GPIO_PIN_11, 0},
    {GPIOA, GPIO_PIN_12, 0},
    {GPIOA, GPIO_PIN_15, 0},
    {GPIOB, GPIO_PIN_3,  0},
    {GPIOB, GPIO_PIN_4,  0},
    {GPIOB, GPIO_PIN_5,  0},
};

char have_line = 0;
void line_detect(void)
{
    for (uint8_t i = 0; i < 8; i++) {
        linepin_list[i].value = HAL_GPIO_ReadPin(linepin_list[i].GPIOx, linepin_list[i].GPIO_Pin);
    }
}
uint8_t get_LedFind_Scan(void)
{
    if (linepin_list[0].value != 0 &&
        linepin_list[1].value != 0 &&
        linepin_list[2].value != 0 &&
        linepin_list[3].value != 0 &&
        linepin_list[4].value != 0 &&
        linepin_list[5].value != 0 &&
        linepin_list[6].value != 0 &&
        linepin_list[7].value != 0)
    {
        have_line = 0;
    } else {
        have_line = 1;
    }

    if (linepin_list[7].value == 0) return 7;
    else if (linepin_list[6].value == 0) return 6;
    else if (linepin_list[5].value == 0) return 5;
    else if (linepin_list[4].value == 0) return 4;
    else if (linepin_list[3].value == 0) return 3;
    else if (linepin_list[2].value == 0) return 2;
    else if (linepin_list[1].value == 0) return 1;
    else if (linepin_list[0].value == 0) return 0;

    return 0;
}
static int Linechange_list[8] = {
    -4, -3, -2, -1, 1, 2, 3, 4
};

char value_led[20];

int Find_Line_Begins(void)
{
    int find_value;
    line_detect();
    find_value = Linechange_list[get_LedFind_Scan()];

    OLED_ShowString(3,8, value_led);  // OLED 显示偏差值

    SetSpeedR(23+find_value);
    SetSpeedL(23-find_value);// 简单比例控制

    return find_value;
}
