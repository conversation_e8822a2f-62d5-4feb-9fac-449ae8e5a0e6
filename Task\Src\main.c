/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "i2c.h"
#include "tim.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "OLED.h"
#include "MPU6050.h"
#include "pid.h"
#include "Motor.h"
#include "Direction.h"
#include "task.h"
#include "key.h"
#include <stm32f1xx_hal_rcc.h>
#include <stm32_hal_legacy.h>
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */
float  g_fMPU6050YawMovePidOut = 0; //姿态PID运算输出
float  g_fMPU6050YawMovePidOut1 = 0; //第一个电机控制输出
float  g_fMPU6050YawMovePidOut2 = 0; //第一个电机控制输出
extern tPid pidMotor1Speed;//速度环用的PID
extern tPid pidMPU6050YawMovement;//角度环用的PID
unsigned char Digtal;//八位数字的存放
unsigned char Anolog[8]={0};//八个模拟量
unsigned char rx_buff[256]={0};//串口用的数组
unsigned char Normal[8]={0};//归一化存放用

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{
  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_I2C2_Init();
  MX_TIM2_Init();
  MX_TIM4_Init();
  /* USER CODE BEGIN 2 */
	OLED_Init();
  PID_init();
  KEY_Init(); // 初始化按键
  HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_3);
  HAL_TIM_PWM_Start(&htim2,TIM_CHANNEL_4);
  HAL_TIM_Encoder_Start(&htim4,TIM_CHANNEL_1);//开启定时器4
  HAL_TIM_Encoder_Start(&htim4,TIM_CHANNEL_2);
  HAL_TIM_Base_Start_IT(&htim4);                //开启定时器4 中断
    // 编码器和电机相关变量（如需要可以取消注释）
    // int Encoder1Count = 0;//编码器计数器值
    // int Encoder2Count = 0;
    // int MotorSpeed;

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */

    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
    Do_count=0;
    
    // 使用KEY模块初始化OLED显示
    KEY_UpdateOLED();
    
    while(1)
    {
      if(KEY_IsSelectionMode())
      {
        // 选择模式：检测按键并更新计时器
        KeyState keyState = KEY_Scan(0);

        // 如果按键按下，切换任务
        if(keyState == KEY_DOWN)
        {
          KEY_SwitchTask();
        }

        // 更新选择模式计时器
        KEY_UpdateSelectionTimer();
      }
      else
      {
        // 执行模式：只执行一次选定的任务
        if(!KEY_IsTaskExecuted())
        {
          uint8_t current_task = KEY_GetTaskIndex();
          switch(current_task)
          {
            case 0:
              task_0();
              break;
            case 1:
              task_1();
              break;
            case 2:
              task_2();
              break;
            case 3:
              task_3();
              break;
            case 4:
              task_4();
              break;
            default:
              task_0();
              break;
          }

          // 标记任务已执行
          KEY_SetTaskExecuted();
        }
        else
        {
          // 任务已执行，系统停止，只更新显示
          // 可以在这里添加LED闪烁等提示
          HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_6); // LED闪烁提示任务完成
        }
      }

      HAL_Delay(100); // 适当延时，避免CPU占用过高
    }
  /* USER CODE END 3 */

}
/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.HSEPredivValue = RCC_HSE_PREDIV_DIV1;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLMUL = RCC_PLL_MUL9;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */