#!/bin/bash
# 智能解析Keil .uvprojx XML文件的辅助脚本

# 使用awk和sed组合解析XML
parse_xml_tag() {
    local file="$1"
    local tag="$2"
    local context="${3:-}"
    
    if [[ -n "$context" ]]; then
        # 在特定上下文中查找
        awk -v tag="$tag" -v ctx="$context" '
        BEGIN { in_context = 0; found = 0 }
        $0 ~ "<" ctx ">" { in_context = 1 }
        $0 ~ "</" ctx ">" { in_context = 0 }
        in_context && $0 ~ "<" tag ">" {
            gsub(/^.*</ tag ">/, "")
            gsub(/<\// tag ">.*$/, "")
            gsub(/^[ \t]+|[ \t]+$/, "")
            print
            found = 1
        }
        ' "$file"
    else
        # 全局查找
        grep -o "<$tag>[^<]*</$tag>" "$file" 2>/dev/null | \
        sed "s/<$tag>//;s/<\/$tag>//" | \
        sed 's/^[ \t]*//;s/[ \t]*$//'
    fi
}

# 解析所有匹配的标签
parse_xml_tag_all() {
    local file="$1"
    local tag="$2"
    grep -o "<$tag>[^<]*</$tag>" "$file" 2>/dev/null | \
    sed "s/<$tag>//;s/<\/$tag>//" | \
    sed 's/^[ \t]*//;s/[ \t]*$//'
}

# 解析嵌套的文件路径
parse_file_paths() {
    local file="$1"
    awk '
    /<File>/ { in_file = 1 }
    /<\/File>/ { in_file = 0 }
    in_file && /<FilePath>/ {
        gsub(/^.*<FilePath>/, "")
        gsub(/<\/FilePath>.*$/, "")
        gsub(/^[ \t]+|[ \t]+$/, "")
        print
    }
    ' "$file"
}

# 解析Target信息
parse_target_info() {
    local file="$1"
    local target_name="$2"
    
    awk -v target="$target_name" '
    BEGIN { in_target = 0; found_target = 0 }
    /<Target>/ { in_target = 1; target_section = "" }
    /<\/Target>/ { 
        if (found_target) {
            in_target = 0
            found_target = 0
        }
    }
    in_target { target_section = target_section "\n" $0 }
    in_target && /<TargetName>/ {
        gsub(/^.*<TargetName>/, "", $0)
        gsub(/<\/TargetName>.*$/, "", $0)
        gsub(/^[ \t]+|[ \t]+$/, "", $0)
        if ($0 == target || target == "") {
            found_target = 1
            print "FOUND_TARGET:" $0
        }
    }
    found_target && /<Device>/ {
        gsub(/^.*<Device>/, "", $0)
        gsub(/<\/Device>.*$/, "", $0)
        gsub(/^[ \t]+|[ \t]+$/, "", $0)
        print "DEVICE:" $0
    }
    found_target && /<Cpu>/ {
        gsub(/^.*<Cpu>/, "", $0)
        gsub(/<\/Cpu>.*$/, "", $0)
        gsub(/^[ \t]+|[ \t]+$/, "", $0)
        print "CPU:" $0
    }
    ' "$file"
}

# 解析编译选项
parse_compile_options() {
    local file="$1"
    
    echo "# 编译选项"
    
    # 优化级别
    local opt=$(parse_xml_tag "$file" "Optimize" "Cads")
    echo "OPTIMIZE:$opt"
    
    # 警告级别
    local warn=$(parse_xml_tag "$file" "wLevel" "Cads")
    echo "WARNING_LEVEL:$warn"
    
    # 调试信息
    local debug=$(parse_xml_tag "$file" "Debug" "Cads")
    echo "DEBUG:$debug"
    
    # C99模式
    local c99=$(parse_xml_tag "$file" "uC99" "Cads")
    echo "C99:$c99"
    
    # GNU扩展
    local gnu=$(parse_xml_tag "$file" "uGnu" "Cads")
    echo "GNU:$gnu"
}

# 解析定义
parse_defines() {
    local file="$1"
    
    echo "# 宏定义"
    local defines=$(parse_xml_tag "$file" "Define" "Cads")
    if [[ -n "$defines" ]]; then
        # 将逗号分隔的定义转换为单独的行
        echo "$defines" | tr ',' '\n' | sed 's/^[ \t]*//;s/[ \t]*$//' | while read -r define; do
            [[ -n "$define" ]] && echo "DEFINE:$define"
        done
    fi
}

# 解析包含路径
parse_include_paths() {
    local file="$1"
    
    echo "# 包含路径"
    local includes=$(parse_xml_tag "$file" "IncludePath" "Cads")
    if [[ -n "$includes" ]]; then
        # 将分号分隔的路径转换为单独的行
        echo "$includes" | tr ';' '\n' | sed 's/^[ \t]*//;s/[ \t]*$//' | while read -r path; do
            [[ -n "$path" ]] && echo "INCLUDE:$path"
        done
    fi
}

# 解析杂项选项
parse_misc_controls() {
    local file="$1"
    
    echo "# 杂项选项"
    local misc=$(parse_xml_tag "$file" "MiscControls" "Cads")
    [[ -n "$misc" ]] && echo "MISC_CONTROLS:$misc"
}

# 解析链接选项
parse_linker_options() {
    local file="$1"
    
    echo "# 链接选项"
    
    # 使用库
    local uselib=$(parse_xml_tag "$file" "uSL" "LDads")
    echo "USE_LIBRARY:$uselib"
    
    # 使用数学库
    local usemath=$(parse_xml_tag "$file" "uMathLib" "LDads")
    echo "USE_MATH_LIB:$usemath"
    
    # 散列文件
    local scatter=$(parse_xml_tag "$file" "ScatterFile" "LDads")
    [[ -n "$scatter" ]] && echo "SCATTER_FILE:$scatter"
    
    # 杂项链接选项
    local misc_ld=$(parse_xml_tag "$file" "Misc" "LDads")
    [[ -n "$misc_ld" ]] && echo "MISC_LINKER:$misc_ld"
}

# 解析组和文件
parse_groups_and_files() {
    local file="$1"
    
    echo "# 文件列表"
    awk '
    BEGIN { 
        in_group = 0
        in_file = 0
        group_name = ""
        group_level = 0
    }
    /<Group>/ { 
        in_group = 1
        group_level++
    }
    /<\/Group>/ { 
        group_level--
        if (group_level == 0) {
            in_group = 0
            group_name = ""
        }
    }
    in_group && /<GroupName>/ {
        gsub(/^.*<GroupName>/, "")
        gsub(/<\/GroupName>.*$/, "")
        gsub(/^[ \t]+|[ \t]+$/, "")
        if (group_level == 1) {
            group_name = $0
            print "GROUP:" group_name
        }
    }
    in_group && /<File>/ { in_file = 1 }
    in_group && /<\/File>/ { in_file = 0 }
    in_file && /<FileName>/ {
        gsub(/^.*<FileName>/, "")
        gsub(/<\/FileName>.*$/, "")
        gsub(/^[ \t]+|[ \t]+$/, "")
        file_name = $0
    }
    in_file && /<FileType>/ {
        gsub(/^.*<FileType>/, "")
        gsub(/<\/FileType>.*$/, "")
        gsub(/^[ \t]+|[ \t]+$/, "")
        file_type = $0
    }
    in_file && /<FilePath>/ {
        gsub(/^.*<FilePath>/, "")
        gsub(/<\/FilePath>.*$/, "")
        gsub(/^[ \t]+|[ \t]+$/, "")
        file_path = $0
        print "FILE:" group_name ":" file_type ":" file_path
    }
    ' "$file"
}

# 主解析函数
parse_keil_project() {
    local uvprojx_file="$1"
    
    if [[ ! -f "$uvprojx_file" ]]; then
        echo "错误: 文件不存在 - $uvprojx_file" >&2
        return 1
    fi
    
    echo "# Keil工程解析结果"
    echo "# 文件: $uvprojx_file"
    echo "# 时间: $(date)"
    echo ""
    
    # 解析基本信息
    echo "# 基本信息"
    local header=$(parse_xml_tag "$uvprojx_file" "Header")
    [[ -n "$header" ]] && echo "HEADER:$header"
    
    # 解析目标信息
    parse_target_info "$uvprojx_file" ""
    echo ""
    
    # 解析编译选项
    parse_compile_options "$uvprojx_file"
    echo ""
    
    # 解析定义
    parse_defines "$uvprojx_file"
    echo ""
    
    # 解析包含路径
    parse_include_paths "$uvprojx_file"
    echo ""
    
    # 解析链接选项
    parse_linker_options "$uvprojx_file"
    echo ""
    
    # 解析文件列表
    parse_groups_and_files "$uvprojx_file"
}

# 如果直接运行脚本，执行解析
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    if [[ $# -eq 0 ]]; then
        echo "用法: $0 <uvprojx文件>"
        exit 1
    fi
    
    parse_keil_project "$1"
fi