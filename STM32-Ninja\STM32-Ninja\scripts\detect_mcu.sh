#!/bin/bash
# STM32 Ninja - MCU自动检测脚本

# 加载通用函数
source "$(dirname "$0")/common.sh"

# MCU配置映射表
declare -A MCU_CONFIGS=(
    # STM32F1系列
    ["0x10016410"]="stm32f1x.cfg"      # STM32F10x Medium-density
    ["0x10016412"]="stm32f1x.cfg"      # STM32F10x Low-density
    ["0x10016414"]="stm32f1x.cfg"      # STM32F10x High-density
    ["0x10016430"]="stm32f1x.cfg"      # STM32F10x XL-density
    ["0x10016418"]="stm32f1x.cfg"      # STM32F10x Connectivity
    
    # STM32F4系列
    ["0x10076413"]="stm32f4x.cfg"      # STM32F405/407/415/417
    ["0x10076419"]="stm32f4x.cfg"      # STM32F42x/43x
    ["0x10076423"]="stm32f4x.cfg"      # STM32F401
    ["0x10076431"]="stm32f4x.cfg"      # STM32F411
    ["0x10076433"]="stm32f4x.cfg"      # STM32F446
    ["0x10076435"]="stm32f4x.cfg"      # STM32F469/479
    
    # STM32F0系列
    ["0x10006440"]="stm32f0x.cfg"      # STM32F0x0
    ["0x10006444"]="stm32f0x.cfg"      # STM32F03x
    ["0x10006445"]="stm32f0x.cfg"      # STM32F04x
    ["0x10006448"]="stm32f0x.cfg"      # STM32F07x
    
    # STM32F2系列
    ["0x10016411"]="stm32f2x.cfg"      # STM32F2xx
    
    # STM32F3系列
    ["0x10016422"]="stm32f3x.cfg"      # STM32F302/303
    ["0x10016432"]="stm32f3x.cfg"      # STM32F373
    ["0x10016438"]="stm32f3x.cfg"      # STM32F334
    ["0x10016439"]="stm32f3x.cfg"      # STM32F301
    
    # STM32F7系列
    ["0x10016449"]="stm32f7x.cfg"      # STM32F74x/75x
    ["0x10016451"]="stm32f7x.cfg"      # STM32F76x/77x
    ["0x10016452"]="stm32f7x.cfg"      # STM32F72x/73x
    
    # STM32H7系列
    ["0x10016450"]="stm32h7x.cfg"      # STM32H74x/75x
    ["0x10016480"]="stm32h7x.cfg"      # STM32H7A3/7B3
    
    # STM32L0系列
    ["0x10086417"]="stm32l0.cfg"       # STM32L0xx Cat.1
    ["0x10086425"]="stm32l0.cfg"       # STM32L0xx Cat.2
    ["0x10086427"]="stm32l0.cfg"       # STM32L0xx Cat.3
    ["0x10086447"]="stm32l0.cfg"       # STM32L0xx Cat.5
    
    # STM32L1系列
    ["0x10086416"]="stm32l1.cfg"       # STM32L1xx Cat.1/2
    ["0x10086427"]="stm32l1.cfg"       # STM32L1xx Cat.3
    ["0x10086436"]="stm32l1.cfg"       # STM32L1xx Cat.4/5/6
    
    # STM32L4系列
    ["0x10076415"]="stm32l4x.cfg"      # STM32L47x/48x
    ["0x10076435"]="stm32l4x.cfg"      # STM32L43x/44x
    ["0x10076461"]="stm32l4x.cfg"      # STM32L496/4A6
    ["0x10076462"]="stm32l4x.cfg"      # STM32L45x/46x
    ["0x10076464"]="stm32l4x.cfg"      # STM32L412/422
    
    # STM32G0系列
    ["0x10006460"]="stm32g0x.cfg"      # STM32G07x/08x
    ["0x10006466"]="stm32g0x.cfg"      # STM32G03x/04x
    
    # STM32G4系列
    ["0x10016468"]="stm32g4x.cfg"      # STM32G431/441
    ["0x10016469"]="stm32g4x.cfg"      # STM32G47x/48x
)

# 检测MCU型号
detect_mcu() {
    local openocd=$(find_openocd)
    if [ -z "$openocd" ]; then
        error "未找到OpenOCD"
        return 1
    fi
    
    # 获取烧录器类型
    local programmer=$(detect_programmer)
    if [ -z "$programmer" ]; then
        error "未检测到烧录器"
        return 1
    fi
    
    # 创建临时配置文件用于读取设备ID
    local temp_cfg=$(mktemp -t openocd_detect_XXXXXX.cfg)
    
    # 根据烧录器类型生成配置
    if [ "$programmer" == "cmsis-dap" ]; then
        cat > "$temp_cfg" << EOF
source [find interface/cmsis-dap.cfg]
transport select swd
adapter speed 1000

# 通用cortex-m配置
set CHIPNAME auto_detect
set ENDIAN little
set WORKAREASIZE 0x1000

target create auto_detect.cpu cortex_m -endian \$ENDIAN -chain-position auto_detect.cpu

init
cortex_m reset_config srst
halt

# 读取设备ID
set device_id [mrw 0xE0042000]
echo "DEVICE_ID:\$device_id"

exit
EOF
    else
        cat > "$temp_cfg" << EOF
source [find interface/${programmer}.cfg]
transport select hla_swd
adapter speed 1000

# 通用cortex-m配置
set CHIPNAME auto_detect
set ENDIAN little
set WORKAREASIZE 0x1000

target create auto_detect.cpu cortex_m -endian \$ENDIAN -chain-position auto_detect.cpu

init
cortex_m reset_config srst
halt

# 读取设备ID
set device_id [mrw 0xE0042000]
echo "DEVICE_ID:\$device_id"

exit
EOF
    fi
    
    # 执行检测
    step "检测MCU型号..."
    local device_id=$($openocd -f "$temp_cfg" 2>&1 | grep "DEVICE_ID:" | sed 's/DEVICE_ID://')
    rm -f "$temp_cfg"
    
    if [ -z "$device_id" ]; then
        error "无法读取设备ID"
        return 1
    fi
    
    # 查找对应的配置文件
    local config_file="${MCU_CONFIGS[$device_id]}"
    if [ -z "$config_file" ]; then
        warn "未知的设备ID: $device_id"
        warn "将使用通用Cortex-M配置"
        echo "cortex_m"
        return 0
    fi
    
    info "检测到MCU: 设备ID=$device_id, 配置文件=$config_file"
    echo "$config_file"
    return 0
}

# 主程序
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    detect_mcu
fi