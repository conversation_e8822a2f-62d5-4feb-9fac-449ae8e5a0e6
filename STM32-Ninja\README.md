# STM32 Ninja 🥷

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Platform](https://img.shields.io/badge/Platform-Windows%20%7C%20Linux%20%7C%20macOS-blue)](https://github.com/stm32-ninja/stm32-ninja)
[![STM32](https://img.shields.io/badge/STM32-Supported-green)](https://www.st.com/en/microcontrollers-microprocessors/stm32-32-bit-arm-cortex-mcus.html)

[English](#english) | [简体中文](#简体中文)

---

## English

Smart STM32 build and flash tool that makes STM32 development simple, efficient, and AI-friendly.

### ✨ Features

- 🚀 **Lightning Fast** - Uses Ninja build system for parallel compilation
- 🔄 **Smart Conversion** - Auto-converts Keil/IAR projects to modern build system
- 🤖 **AI-Friendly** - Command-line interface designed for AI tool integration
- 🎯 **Beginner Friendly** - Interactive menu system, one-click build & flash
- 🔧 **Fully Automated** - Smart detection of MCU, toolchain, and programmer
- 📦 **Zero Configuration** - Works out of the box with automatic dependency handling
- 📁 **Dynamic Management** - Add/remove source files without regenerating project

### 📋 System Requirements

⚠️ **IMPORTANT: STM32CubeCLT Installation**

**You MUST install STM32CubeCLT in the D:\ root directory:**
```
D:\STM32CubeCLT_1.18.0\
```

Download STM32CubeCLT from: https://www.st.com/en/development-tools/stm32cubeclt.html

**Why D:\ drive?**
- Avoids path issues with spaces in "Program Files"
- Consistent path across different Windows installations
- Faster access and compilation

Other requirements:
- **OS**: Windows 10+ (with Git Bash), Linux, macOS
- **Programmer**: ST-Link, J-Link, DAPLink, CMSIS-DAP
- **Dependencies**: Git Bash (Windows)

### 🚀 Quick Start

```bash
# 1. Clone repository
git clone https://github.com/McuXifeng/STM32-Ninja.git
cd STM32-Ninja

# 2. Global installation
./global_install.sh
# Choose /d/STM32Ninja as installation path

# 3. Initialize your project
cd /path/to/your/stm32/project
stm32ninja init

# 4. Build and flash
./ninja.sh build
./ninja.sh flash
```

### 📖 Command Reference

```bash
# Build commands
./ninja.sh build              # Build with default settings
./ninja.sh build --type Release --jobs 16

# Flash commands
./ninja.sh flash              # Auto-detect programmer
./ninja.sh flash --programmer jlink --speed 4000

# File management
./ninja.sh list               # List all source files
./ninja.sh add src/new.c      # Add single file
./ninja.sh add -r libs/       # Add directory recursively
./ninja.sh remove old.c       # Remove file
```

For more details, see [AI_PROMPT.md](AI_PROMPT.md) for AI integration guide.

---

## 简体中文

智能的STM32编译烧录工具，让STM32开发变得简单、高效、AI友好。

### ✨ 特性

- 🚀 **极速编译** - 使用Ninja构建系统，充分利用多核CPU并行编译
- 🔄 **智能转换** - 自动将Keil/IAR工程转换为现代构建系统
- 🤖 **AI友好** - 支持命令行操作，便于AI工具直接调用
- 🎯 **新手友好** - 图形化交互界面，一键编译烧录
- 🔧 **全自动化** - 智能检测MCU型号、工具链、烧录器
- 📦 **零配置** - 开箱即用，自动处理依赖关系
- 📁 **动态管理** - 支持动态添加/移除源文件，无需重新生成工程

### 📋 系统要求

⚠️ **重要提示：STM32CubeCLT 安装要求**

**必须将 STM32CubeCLT 安装在 D:\ 盘根目录：**
```
D:\STM32CubeCLT_1.18.0\
```

从这里下载 STM32CubeCLT：https://www.st.com/en/development-tools/stm32cubeclt.html

**为什么要安装在 D:\ 盘？**
- 避免 "Program Files" 中空格导致的路径问题
- 确保所有Windows系统上的路径一致性
- 提供更快的访问和编译速度

其他要求：
- **操作系统**: Windows 10+（需要Git Bash）, Linux, macOS
- **烧录器**: ST-Link, J-Link, DAPLink, CMSIS-DAP
- **依赖**: Git Bash（Windows）

### 🚀 快速开始

#### 全局安装（推荐）

```bash
# 1. 克隆项目
git clone https://github.com/McuXifeng/STM32-Ninja.git
cd STM32-Ninja

# 2. 运行全局安装脚本
./global_install.sh

# 3. 选择安装位置
# 强烈推荐选择: /d/STM32Ninja

# 4. 使环境变量生效
source ~/.bashrc  # Linux/macOS
# Windows用户需要重启Git Bash
```

#### 在项目中使用

```bash
# 1. 进入STM32项目目录
cd /path/to/your/stm32/project

# 2. 初始化STM32 Ninja
stm32ninja init

# 3. 使用交互式菜单
./ninja.sh

# 或直接执行命令
./ninja.sh build    # 编译项目
./ninja.sh flash    # 烧录程序
./ninja.sh add lib/my_lib.c    # 添加文件到工程
```

### 📖 使用指南

#### 交互式菜单

运行 `./ninja.sh` 进入交互式菜单：

```
╔════════════════════════════════════════╗
║        STM32 Ninja 主菜单             ║
╠════════════════════════════════════════╣
║  1) 🔨 编译项目                        ║
║  2) 📥 烧录程序                        ║
║  3) 🚀 编译并烧录                      ║
║  4) 📁 管理源文件                      ║
║  5) ⚙️ 设置                           ║
║  6) ❓ 帮助                           ║
║  0) 🚪 退出                           ║
╚════════════════════════════════════════╝
```

#### 命令行模式

##### 编译和烧录
```bash
# 编译项目
./ninja.sh build [--type Debug|Release] [--jobs N]

# 烧录程序
./ninja.sh flash [--device /dev/ttyUSB0]

# 编译并烧录
./ninja.sh all
```

##### 文件管理
```bash
# 列出当前工程中的所有源文件
./ninja.sh list

# 添加单个文件
./ninja.sh add src/my_module.c

# 添加整个目录（非递归）
./ninja.sh add drivers/

# 递归添加目录中的所有源文件
./ninja.sh add -r libraries/my_lib/

# 添加特定类型的文件
./ninja.sh add -p "*.c" drivers/

# 添加文件到指定分组
./ninja.sh add -g "Middleware" lib/middleware.c

# 移除文件
./ninja.sh remove test.c
./ninja.sh remove "test/*.c"
```

### 支持的项目类型

1. **Keil MDK工程** (.uvprojx)
   - 自动解析工程配置
   - 智能转换为Ninja构建
   - 支持AC5/AC6编译器

2. **STM32CubeMX工程**
   - 支持Makefile项目
   - 支持CMake项目
   - 自动检测HAL/LL库

3. **自定义工程**
   - 提供项目模板
   - 支持自定义构建配置

### 🤖 AI集成指南

STM32 Ninja专为AI工具设计，支持完整的命令行操作：

#### 基本用法示例

```bash
# AI可以通过以下命令序列完成开发任务：

# 1. 进入项目目录并初始化
cd /h/MyProject/STM32F103
stm32ninja init

# 2. 添加新的驱动文件
./ninja.sh add drivers/lcd_driver.c
./ninja.sh add drivers/lcd_driver.h

# 3. 编译项目
./ninja.sh build

# 4. 如果有错误，自动修复并重试
./ninja.sh build --fix-errors

# 5. 烧录到设备
./ninja.sh flash
```

#### AI友好特性

1. **所有操作都可通过命令行完成** - 无需手动编辑配置文件
2. **智能错误修复** - 自动处理常见编译错误
3. **清晰的输出** - 结构化的成功/错误信息
4. **状态持久化** - 编译信息自动保存供烧录使用

详细的AI集成指南请参考 [AI_PROMPT.md](AI_PROMPT.md)。

### 🛠️ 高级配置

#### 配置文件

配置文件位置：`config/ninja.conf`

```bash
# 工具链配置
TOOLCHAIN_PATH=/d/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin
COMPILER_PREFIX=arm-none-eabi-

# 烧录器配置
PROGRAMMER_TYPE=stlink    # stlink, jlink, daplink, cmsis-dap
FLASH_SPEED=4000          # kHz
TRANSPORT=swd             # swd, jtag

# 构建选项
DEFAULT_BUILD_TYPE=Debug
DEFAULT_JOBS=8
NINJA_STATUS=[%f/%t] %es
```

### 🐛 故障排除

#### 常见问题

1. **找不到编译器**
   - 确保STM32CubeCLT安装在 `D:\STM32CubeCLT_1.18.0\`
   - 运行 `./ninja.sh settings` 手动设置路径

2. **路径包含空格错误**
   - 不要安装在 "Program Files" 目录
   - 使用推荐的 D:\ 盘安装路径

3. **烧录失败**
   ```bash
   # 检查烧录器连接
   ./ninja.sh settings  # 选择烧录器配置
   ```

4. **Keil工程转换失败**
   ```bash
   # 使用智能修复
   ./ninja.sh build --fix-errors
   
   # 查看详细日志
   cat build/build_error.log
   ```

### 📝 项目结构

```
stm32-ninja/
├── scripts/              # 核心脚本
│   ├── build.sh         # 编译脚本
│   ├── flash.sh         # 烧录脚本
│   ├── add_files.sh     # 添加文件脚本
│   ├── remove_files.sh  # 移除文件脚本
│   ├── common.sh        # 通用函数
│   └── keil_to_ninja.sh # Keil转换
├── config/              # 配置文件
├── global_install.sh    # 全局安装脚本
└── ninja.sh            # 主程序入口
```

### 🤝 贡献指南

欢迎贡献代码！

#### 开发环境设置

```bash
# 克隆并创建开发分支
git clone https://github.com/McuXifeng/STM32-Ninja.git
cd STM32-Ninja
git checkout -b feature/your-feature

# 提交代码
git add .
git commit -m "Add your feature"
git push origin feature/your-feature
```

### 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

版权所有 © 2025 米醋电子工作室 (Michu Electronics Studio)

### 🙏 致谢

- [Ninja Build](https://ninja-build.org/) - 高性能构建系统
- [OpenOCD](http://openocd.org/) - 开源片上调试器
- [STM32CubeCLT](https://www.st.com/en/development-tools/stm32cubeclt.html) - STM32命令行工具
