#!/bin/bash
# STM32 Ninja - 主程序 v2.0 (全局安装版本)

# 终端颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly WHITE='\033[1;37m'
readonly NC='\033[0m' # No Color

# 输出函数
info() { echo -e "${BLUE}[信息]${NC} $1"; }
success() { echo -e "${GREEN}[成功]${NC} $1"; }
warn() { echo -e "${YELLOW}[警告]${NC} $1"; }
error() { echo -e "${RED}[错误]${NC} $1"; }
step() { echo -e "${CYAN}➤${NC} $1"; }

# 获取脚本目录
if [ -n "$STM32_NINJA_HOME" ]; then
    SCRIPT_DIR="$STM32_NINJA_HOME/scripts"
else
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
fi

# 项目目录
PROJECT_DIR="${NINJA_PROJECT_DIR:-$(pwd)}"
cd "$PROJECT_DIR" || exit 1

# 暂停函数
pause() {
    echo ""
    echo "按任意键继续..."
    read -n 1 -s
}

# 显示Logo
show_logo() {
    clear
    echo -e "${CYAN}"
    cat << 'EOF'
     _____ _______ __  __ ____ ___    _   _ _       _       
    / ____|__   __|  \/  |___ \__ \  | \ | (_)     (_)      
   | (___    | |  | \  / | __) | ) | |  \| |_ _ __  _  __ _ 
    \___ \   | |  | |\/| ||__ < / /  | . ` | | '_ \| |/ _` |
    ____) |  | |  | |  | |___) / /_  | |\  | | | | | | (_| |
   |_____/   |_|  |_|  |_|____/____| |_| \_|_|_| |_|_|\__,_|
                                                             
EOF
    echo -e "${WHITE}        智能STM32编译烧录工具 v2.0${NC}"
    echo -e "${WHITE}    让STM32开发变得简单 | AI友好 | 新手友好${NC}"
    echo ""
}

# 检测项目类型
detect_project_type() {
    if ls *.uvprojx &>/dev/null || ls */*.uvprojx &>/dev/null; then
        echo "keil"
    elif [ -f "CMakeLists.txt" ]; then
        echo "cmake"
    elif [ -f "Makefile" ]; then
        echo "makefile"
    else
        echo "unknown"
    fi
}

# 第一次运行向导
first_run_wizard() {
    show_logo
    echo -e "${WHITE}项目初始化向导${NC}"
    echo "═══════════════════════════════════"
    echo ""
    
    # 检测项目类型
    local project_type=$(detect_project_type)
    
    case "$project_type" in
        keil)
            local keil_file=$(find . -name "*.uvprojx" | head -1)
            success "检测到Keil工程"
            info "工程文件: $keil_file"
            echo ""
            echo "Keil工程需要转换才能使用命令行编译。"
            echo -n "是否自动转换为Ninja格式? (Y/n): "
            read -n 1 gen_ninja
            echo ""
            if [ "$gen_ninja" != "n" ] && [ "$gen_ninja" != "N" ]; then
                if [ -f "$SCRIPT_DIR/keil_to_ninja.sh" ]; then
                    "$SCRIPT_DIR/keil_to_ninja.sh"
                else
                    error "未找到转换脚本"
                fi
            fi
            ;;
        cmake)
            success "检测到CMake工程"
            ;;
        makefile)
            success "检测到Makefile工程"
            ;;
        *)
            warn "未检测到已知的工程类型"
            echo "支持的类型: Keil (*.uvprojx), CMake, Makefile"
            ;;
    esac
    
    # 创建项目配置
    mkdir -p config
    cat > config/ninja.conf << EOF
# STM32 Ninja 项目配置
PROJECT_TYPE=$project_type
INIT_DATE=$(date +%Y-%m-%d)
EOF
    
    echo ""
    success "初始化完成！"
    echo ""
    echo "按任意键继续..."
    read -n 1 -s
}

# 编译项目
compile_project() {
    show_logo
    echo -e "${WHITE}编译项目${NC}"
    echo "═══════════════════════════════════"
    echo ""
    
    # 调用编译脚本
    if [ -f "$SCRIPT_DIR/build.sh" ]; then
        "$SCRIPT_DIR/build.sh" --project "$PROJECT_DIR" "$@"
    else
        error "未找到编译脚本"
        return 1
    fi
}

# 烧录项目
flash_project() {
    show_logo
    echo -e "${WHITE}烧录程序${NC}"
    echo "═══════════════════════════════════"
    echo ""
    
    # 调用烧录脚本
    if [ -f "$SCRIPT_DIR/flash.sh" ]; then
        "$SCRIPT_DIR/flash.sh" --project "$PROJECT_DIR" "$@"
    else
        error "未找到烧录脚本"
        return 1
    fi
}

# 编译并烧录
compile_and_flash() {
    compile_project "$@"
    if [ $? -eq 0 ]; then
        echo ""
        flash_project
    fi
}

# 烧录器设置
programmer_settings() {
    show_logo
    echo -e "${WHITE}烧录器配置${NC}"
    echo "═══════════════════════════════════"
    echo ""
    
    # 加载通用函数
    if [ -f "$SCRIPT_DIR/common.sh" ]; then
        source "$SCRIPT_DIR/common.sh"
    fi
    
    # 显示当前检测到的烧录器
    echo "当前检测到的烧录器："
    local current_programmer=$(detect_programmer 2>/dev/null)
    if [ -n "$current_programmer" ]; then
        success "已检测到: $current_programmer"
    else
        warn "未检测到烧录器"
    fi
    
    echo ""
    echo "配置选项："
    echo "  1) 检测烧录器"
    echo "  2) 设置烧录速度"
    echo "  3) 高级设置"
    echo "  0) 返回"
    echo ""
    echo -n "请选择 [0-3]: "
    read -n 1 choice
    echo ""
    
    case $choice in
        1) 
            if [ -f "$SCRIPT_DIR/detect_programmer.sh" ]; then
                "$SCRIPT_DIR/detect_programmer.sh"
            fi
            ;;
        2) 
            echo "输入烧录速度 (kHz) [默认: 1000]: "
            read speed
            speed=${speed:-1000}
            echo "PROGRAMMER_SPEED=\"$speed\"" >> config/ninja.conf
            success "速度设置为: ${speed} kHz"
            ;;
        3) 
            info "高级设置功能开发中..."
            ;;
    esac
    
    echo ""
    echo "按任意键继续..."
    read -n 1 -s
}

# 管理源文件
manage_files() {
    while true; do
        show_logo
        echo -e "${WHITE}源文件管理${NC}"
        echo "═══════════════════════════════════"
        echo ""
        echo "  1) 📋 列出当前源文件"
        echo "  2) ➕ 添加文件到工程"
        echo "  3) ➕ 添加目录到工程"
        echo "  4) ➖ 移除文件"
        echo "  5) 🔍 搜索文件"
        echo "  0) 🔙 返回"
        echo ""
        echo -n "请选择操作 [0-5]: "
        read -n 1 choice
        echo ""
        
        case $choice in
            1)
                echo ""
                if [ -f "$SCRIPT_DIR/add_files.sh" ]; then
                    "$SCRIPT_DIR/add_files.sh"
                else
                    error "找不到add_files.sh脚本"
                fi
                pause
                ;;
            2)
                echo ""
                echo -n "请输入要添加的文件路径: "
                read file_path
                if [ -n "$file_path" ]; then
                    if [ -f "$SCRIPT_DIR/add_files.sh" ]; then
                        "$SCRIPT_DIR/add_files.sh" "$file_path"
                    else
                        error "找不到add_files.sh脚本"
                    fi
                fi
                pause
                ;;
            3)
                echo ""
                echo -n "请输入要添加的目录路径: "
                read dir_path
                if [ -n "$dir_path" ]; then
                    echo -n "是否递归添加所有子目录? (y/N): "
                    read -n 1 recursive
                    echo ""
                    if [[ "$recursive" == "y" || "$recursive" == "Y" ]]; then
                        if [ -f "$SCRIPT_DIR/add_files.sh" ]; then
                            "$SCRIPT_DIR/add_files.sh" -r "$dir_path"
                        fi
                    else
                        if [ -f "$SCRIPT_DIR/add_files.sh" ]; then
                            "$SCRIPT_DIR/add_files.sh" "$dir_path"
                        fi
                    fi
                fi
                pause
                ;;
            4)
                echo ""
                echo -n "请输入要移除的文件模式: "
                read pattern
                if [ -n "$pattern" ]; then
                    if [ -f "$SCRIPT_DIR/remove_files.sh" ]; then
                        "$SCRIPT_DIR/remove_files.sh" "$pattern"
                    else
                        error "找不到remove_files.sh脚本"
                    fi
                fi
                pause
                ;;
            5)
                echo ""
                echo -n "请输入搜索模式: "
                read pattern
                if [ -n "$pattern" ]; then
                    info "搜索文件: $pattern"
                    grep -n ": cc\|: as" build.ninja 2>/dev/null | grep "$pattern" || warn "未找到匹配的文件"
                fi
                pause
                ;;
            0)
                return
                ;;
            *)
                error "无效选择"
                pause
                ;;
        esac
    done
}

# 设置菜单
settings_menu() {
    show_logo
    echo -e "${WHITE}设置${NC}"
    echo "═══════════════════════════════════"
    echo ""
    echo "  1) 工具路径设置"
    echo "  2) 烧录器配置"
    echo "  3) 重新初始化项目"
    echo "  0) 返回"
    echo ""
    echo -n "请选择 [0-3]: "
    read -n 1 choice
    echo ""
    
    case $choice in
        1) 
            info "工具路径设置..."
            if [ -f "$SCRIPT_DIR/setup_tools.sh" ]; then
                "$SCRIPT_DIR/setup_tools.sh"
            fi
            ;;
        2) programmer_settings ;;
        3) 
            rm -f config/ninja.conf
            first_run_wizard
            ;;
    esac
}

# 显示帮助
show_help() {
    show_logo
    echo -e "${WHITE}帮助文档${NC}"
    echo "═══════════════════════════════════"
    echo ""
    echo "STM32 Ninja 是一个智能的STM32开发工具。"
    echo ""
    echo "命令行用法："
    echo "  ./ninja.sh          # 交互式菜单"
    echo "  ./ninja.sh build    # 编译项目"
    echo "  ./ninja.sh flash    # 烧录程序"
    echo "  ./ninja.sh all      # 编译并烧录"
    echo ""
    echo "全局安装: $STM32_NINJA_HOME"
    echo "项目目录: $PROJECT_DIR"
    echo ""
    echo "更多信息请访问: https://github.com/stm32-ninja"
}

# 主菜单
main_menu() {
    while true; do
        show_logo
        echo -e "${WHITE}主菜单${NC}"
        echo "═══════════════════════════════════"
        echo ""
        echo "  1) 🔨 编译项目"
        echo "  2) 📥 烧录程序"
        echo "  3) 🚀 编译并烧录"
        echo "  4) 📁 管理源文件"
        echo "  5) ⚙️  设置"
        echo "  6) ❓ 帮助"
        echo "  0) 🚪 退出"
        echo ""
        echo -n "请选择操作 [0-6]: "
        read -n 1 choice
        echo ""
        
        case $choice in
            1) compile_project ;;
            2) flash_project ;;
            3) compile_and_flash ;;
            4) manage_files ;;
            5) settings_menu ;;
            6) show_help ;;
            0) 
                success "感谢使用 STM32 Ninja！"
                exit 0 
                ;;
            *) error "无效选择，请重试" ;;
        esac
        
        echo ""
        echo "按任意键返回主菜单..."
        read -n 1 -s
    done
}

# 主程序入口
main() {
    # 检查是否第一次运行
    if [ ! -f "config/ninja.conf" ] && [ ! -f ".stm32ninja" ]; then
        first_run_wizard
    fi
    
    # 加载配置
    [ -f "config/ninja.conf" ] && source config/ninja.conf
    [ -f ".stm32ninja" ] && source .stm32ninja
    
    # 根据参数决定运行模式
    case "$1" in
        build|compile)
            shift
            compile_project "$@"
            ;;
        flash|download)
            shift
            flash_project "$@"
            ;;
        all)
            shift
            compile_and_flash "$@"
            ;;
        add)
            shift
            if [ -f "$SCRIPT_DIR/add_files.sh" ]; then
                "$SCRIPT_DIR/add_files.sh" "$@"
            else
                error "找不到add_files.sh脚本"
                exit 1
            fi
            ;;
        remove|rm)
            shift
            if [ -f "$SCRIPT_DIR/remove_files.sh" ]; then
                "$SCRIPT_DIR/remove_files.sh" "$@"
            else
                error "找不到remove_files.sh脚本"
                exit 1
            fi
            ;;
        list|ls)
            if [ -f "$SCRIPT_DIR/add_files.sh" ]; then
                "$SCRIPT_DIR/add_files.sh"
            else
                error "找不到add_files.sh脚本"
                exit 1
            fi
            ;;
        help|--help|-h)
            show_help
            ;;
        version|--version|-v)
            echo "STM32 Ninja v2.0"
            ;;
        *)
            # 交互模式
            main_menu
            ;;
    esac
}

# 信号处理
trap 'echo -e "\n${RED}操作已取消${NC}"; exit 130' INT

# 启动程序
main "$@"