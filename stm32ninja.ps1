# STM32 Ninja PowerShell 包装脚本
# 用于在Windows环境下使用STM32 Ninja框架

param(
    [Parameter(Position=0)]
    [string]$Command,
    
    [Parameter(Position=1, ValueFromRemainingArguments=$true)]
    [string[]]$Arguments
)

# 颜色输出函数
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# 检查项目状态
function Check-ProjectStatus {
    if (Test-Path "build.ninja") {
        Write-Info "STM32 Ninja项目已初始化"
        
        # 读取配置信息
        if (Test-Path "config/ninja.conf") {
            $config = Get-Content "config/ninja.conf"
            foreach ($line in $config) {
                if ($line -match "PROJECT_TYPE=(.+)") {
                    Write-Info "项目类型: $($matches[1])"
                }
                if ($line -match "INIT_DATE=(.+)") {
                    Write-Info "初始化日期: $($matches[1])"
                }
            }
        }
        
        # 检查MCU信息
        if (Test-Path "build/mcu_info.conf") {
            $mcuInfo = Get-Content "build/mcu_info.conf"
            Write-Info "MCU信息: $mcuInfo"
        }
        
        return $true
    } else {
        Write-Error "项目未初始化，请先运行 stm32ninja init"
        return $false
    }
}

# 编译项目
function Build-Project {
    param(
        [string]$BuildType = "Debug",
        [int]$Jobs = 8
    )
    
    if (-not (Check-ProjectStatus)) {
        return
    }
    
    Write-Info "开始编译项目..."
    Write-Info "构建类型: $BuildType"
    Write-Info "并行任务数: $Jobs"
    
    # 使用ninja进行编译
    $startTime = Get-Date
    ninja -j $Jobs
    $endTime = Get-Date
    $duration = $endTime - $startTime
    
    if ($LASTEXITCODE -eq 0) {
        Write-Info "编译成功! 耗时: $($duration.TotalSeconds.ToString('F2'))秒"
        
        # 显示文件大小信息
        if (Test-Path "build/Ide To Keil.elf") {
            $elfFile = Get-Item "build/Ide To Keil.elf"
            Write-Info "ELF文件大小: $($elfFile.Length) bytes"
        }
        
        if (Test-Path "build/Ide To Keil.hex") {
            $hexFile = Get-Item "build/Ide To Keil.hex"
            Write-Info "HEX文件大小: $($hexFile.Length) bytes"
        }
    } else {
        Write-Error "编译失败!"
        
        # 显示错误日志
        if (Test-Path "build_error.log") {
            Write-Warning "错误日志:"
            Get-Content "build_error.log" | Select-Object -Last 10 | ForEach-Object {
                Write-Host "  $_" -ForegroundColor Red
            }
        }
    }
}

# 清理项目
function Clean-Project {
    Write-Info "清理项目..."
    
    if (Test-Path "build") {
        Remove-Item -Recurse -Force "build" -ErrorAction SilentlyContinue
        Write-Info "已清理build目录"
    }
    
    # 清理其他临时文件
    Get-ChildItem -Filter "*.ld" | Remove-Item -Force -ErrorAction SilentlyContinue
    Get-ChildItem -Filter "*.s" | Remove-Item -Force -ErrorAction SilentlyContinue
    
    Write-Info "清理完成"
}

# 列出项目文件
function List-ProjectFiles {
    if (-not (Check-ProjectStatus)) {
        return
    }
    
    Write-Info "项目文件列表:"
    
    # 解析build.ninja文件
    if (Test-Path "build.ninja") {
        $buildFile = Get-Content "build.ninja"
        $sourceFiles = @()
        
        foreach ($line in $buildFile) {
            if ($line -match "build .+: cc (.+\.c)") {
                $sourceFiles += $matches[1]
            }
        }
        
        Write-Host "源文件 ($($sourceFiles.Count)):" -ForegroundColor Cyan
        foreach ($file in $sourceFiles | Sort-Object) {
            if (Test-Path $file) {
                Write-Host "  ✓ $file" -ForegroundColor Green
            } else {
                Write-Host "  ✗ $file" -ForegroundColor Red
            }
        }
    }
}

# 添加文件到项目
function Add-FileToProject {
    param([string]$FilePath)
    
    if (-not (Test-Path $FilePath)) {
        Write-Error "文件不存在: $FilePath"
        return
    }
    
    if (-not $FilePath.EndsWith(".c")) {
        Write-Error "只支持添加.c文件"
        return
    }
    
    Write-Info "添加文件到项目: $FilePath"
    
    # 这里需要修改build.ninja文件
    # 为了简化，建议重新生成项目文件
    Write-Warning "请重新生成项目文件以包含新添加的文件"
}

# 显示帮助信息
function Show-Help {
    Write-Host "STM32 Ninja PowerShell Wrapper Script" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage: .\stm32ninja.ps1 <command> [arguments]" -ForegroundColor White
    Write-Host ""
    Write-Host "Commands:" -ForegroundColor Yellow
    Write-Host "  build     - Build project" -ForegroundColor White
    Write-Host "  clean     - Clean project" -ForegroundColor White
    Write-Host "  list      - List project files" -ForegroundColor White
    Write-Host "  status    - Show project status" -ForegroundColor White
    Write-Host "  help      - Show help information" -ForegroundColor White
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\stm32ninja.ps1 build" -ForegroundColor White
    Write-Host "  .\stm32ninja.ps1 build -Jobs 16" -ForegroundColor White
    Write-Host "  .\stm32ninja.ps1 clean" -ForegroundColor White
    Write-Host "  .\stm32ninja.ps1 list" -ForegroundColor White
    Write-Host "  .\stm32ninja.ps1 status" -ForegroundColor White
    Write-Host ""
    Write-Host "Note: This script must be run in STM32 Ninja project directory" -ForegroundColor Gray
}

# 主逻辑
switch ($Command.ToLower()) {
    "build" {
        $jobs = 8
        if ($Arguments -contains "-Jobs" -or $Arguments -contains "--jobs") {
            $jobsIndex = [Array]::IndexOf($Arguments, "-Jobs")
            if ($jobsIndex -eq -1) { $jobsIndex = [Array]::IndexOf($Arguments, "--jobs") }
            if ($jobsIndex -ge 0 -and $jobsIndex + 1 -lt $Arguments.Length) {
                $jobs = [int]$Arguments[$jobsIndex + 1]
            }
        }
        Build-Project -Jobs $jobs
    }
    "clean" {
        Clean-Project
    }
    "list" {
        List-ProjectFiles
    }
    "status" {
        Check-ProjectStatus | Out-Null
    }
    "help" {
        Show-Help
    }
    default {
        if ([string]::IsNullOrEmpty($Command)) {
            Show-Help
        } else {
            Write-Error "Unknown command: $Command"
            Show-Help
        }
    }
}
