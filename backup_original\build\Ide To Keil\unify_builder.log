[2025-07-16 22:18:11]	[info] incremental build: 31 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\Core\Src\gpio.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\Core\Src\i2c.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\Core\Src\stm32f1xx_hal_msp.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\Core\Src\stm32f1xx_it.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\Core\Src\system_stm32f1xx.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\Core\Src\tim.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_i2c.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Mpu6050Dmp\MPU6050.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Mpu6050Dmp\inv_mpu.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Mpu6050Dmp\inv_mpu_dmp_motion_driver.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\Direction.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\Motor.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\OLED.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\fine_line.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\main.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\pid.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': object (.o) file not exist
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\startup_stm32f103xb.s': object (.o) file not exist

[2025-07-16 22:18:13]	link failed !, exit code: 1
   at unify_builder.Program.Main(String[] args_)
---
Error: L6218E: Undefined symbol KEY_GetTaskIndex (referred from main.o).
Error: L6218E: Undefined symbol KEY_Init (referred from main.o).
Error: L6218E: Undefined symbol KEY_Scan (referred from main.o).
Error: L6218E: Undefined symbol KEY_SwitchTask (referred from main.o).
Error: L6218E: Undefined symbol KEY_UpdateOLED (referred from main.o).
Not enough information to list image symbols.
Finished: 1 information, 0 warning and 5 error messages.


[2025-07-16 22:32:45]	link failed !, exit code: 1
   at unify_builder.Program.Main(String[] args_)
---
Error: L6218E: Undefined symbol KEY_GetTaskIndex (referred from main.o).
Error: L6218E: Undefined symbol KEY_Init (referred from main.o).
Error: L6218E: Undefined symbol KEY_Scan (referred from main.o).
Error: L6218E: Undefined symbol KEY_SwitchTask (referred from main.o).
Error: L6218E: Undefined symbol KEY_UpdateOLED (referred from main.o).
Not enough information to list image symbols.
Finished: 1 information, 0 warning and 5 error messages.


[2025-07-16 22:32:54]	link failed !, exit code: 1
   at unify_builder.Program.Main(String[] args_)
---
Error: L6218E: Undefined symbol KEY_GetTaskIndex (referred from main.o).
Error: L6218E: Undefined symbol KEY_Init (referred from main.o).
Error: L6218E: Undefined symbol KEY_Scan (referred from main.o).
Error: L6218E: Undefined symbol KEY_SwitchTask (referred from main.o).
Error: L6218E: Undefined symbol KEY_UpdateOLED (referred from main.o).
Not enough information to list image symbols.
Finished: 1 information, 0 warning and 5 error messages.


[2025-07-16 22:37:24]	link failed !, exit code: 1
   at unify_builder.Program.Main(String[] args_)
---
Error: L6218E: Undefined symbol KEY_GetTaskIndex (referred from main.o).
Error: L6218E: Undefined symbol KEY_Init (referred from main.o).
Error: L6218E: Undefined symbol KEY_Scan (referred from main.o).
Error: L6218E: Undefined symbol KEY_SwitchTask (referred from main.o).
Error: L6218E: Undefined symbol KEY_UpdateOLED (referred from main.o).
Not enough information to list image symbols.
Finished: 1 information, 0 warning and 5 error messages.


[2025-07-16 22:38:16]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\key.c': object (.o) file not exist

[2025-07-16 22:38:17]	[done]
	build successfully !

[2025-07-17 15:20:33]	[info] incremental build: 5 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\Direction.c': source file has been changed.
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\Motor.c': source file has been changed.
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\key.c': source file has been changed.
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\main.c': source file has been changed.
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-17 15:20:34]	compilation failed at : "c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c", exit code: 1
command: 
  "c:\Keil_v5\ARM\ARMCC\bin\armcc.exe" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I"RTE/_Ide To Keil" -D"USE_HAL_DRIVER" -D"STM32F103xB" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o ".\build\Ide To Keil\.obj\Task\Src\task.o" --no_depend_system_headers --depend ".\build\Ide To Keil\.obj\Task\Src\task.d" .\Task\Src\task.c
   at unify_builder.Program.Main(String[] args_)
---
".\Task\Src\task.c", line 93: Warning:  #223-D: function "delay_ms" declared implicitly
  				delay_ms(200);
  				^
".\Task\Src\task.c", line 282: Warning:  #223-D: function "set_speed" declared implicitly
  					set_speed(5, 8);
  					^
".\Task\Src\task.c", line 283: Warning:  #223-D: function "delay_ms" declared implicitly
  					delay_ms(1000);delay_ms(1000);delay_ms(260);set_speed(8, 8);delay_ms(520);
  					^
".\Task\Src\task.c", line 295: Warning:  #223-D: function "set_speed" declared implicitly
  				set_speed(8, 6);
  				^
".\Task\Src\task.c", line 334: Warning:  #223-D: function "set_speed" declared implicitly
  				set_speed(0, 0);//??
  				^
".\Task\Src\task.c", line 349: Error: At end of source:  #67: expected a "}"
.\Task\Src\task.c: 5 warnings, 1 error

[2025-07-17 15:21:37]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-17 15:21:37]	compilation failed at : "c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c", exit code: 1
command: 
  "c:\Keil_v5\ARM\ARMCC\bin\armcc.exe" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc -I../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I../Drivers/CMSIS/Include -ITask/Src -ITask/Inc -ITask/Mpu6050Dmp -I.cmsis/include -I"RTE/_Ide To Keil" -D"USE_HAL_DRIVER" -D"STM32F103xB" --cpu Cortex-M3 --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o ".\build\Ide To Keil\.obj\Task\Src\task.o" --no_depend_system_headers --depend ".\build\Ide To Keil\.obj\Task\Src\task.d" .\Task\Src\task.c
   at unify_builder.Program.Main(String[] args_)
---
".\Task\Src\task.c", line 93: Warning:  #223-D: function "delay_ms" declared implicitly
  				delay_ms(200);
  				^
".\Task\Src\task.c", line 282: Warning:  #223-D: function "set_speed" declared implicitly
  					set_speed(5, 8);
  					^
".\Task\Src\task.c", line 283: Warning:  #223-D: function "delay_ms" declared implicitly
  					delay_ms(1000);delay_ms(1000);delay_ms(260);set_speed(8, 8);delay_ms(520);
  					^
".\Task\Src\task.c", line 295: Warning:  #223-D: function "set_speed" declared implicitly
  				set_speed(8, 6);
  				^
".\Task\Src\task.c", line 334: Warning:  #223-D: function "set_speed" declared implicitly
  				set_speed(0, 0);//??
  				^
".\Task\Src\task.c", line 349: Error: At end of source:  #67: expected a "}"
.\Task\Src\task.c: 5 warnings, 1 error

[2025-07-17 15:22:41]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-17 15:22:42]	link failed !, exit code: 1
   at unify_builder.Program.Main(String[] args_)
---
Error: L6218E: Undefined symbol delay_ms (referred from task.o).
Error: L6218E: Undefined symbol set_speed (referred from task.o).
Not enough information to list image symbols.
Finished: 1 information, 0 warning and 2 error messages.


[2025-07-17 15:29:29]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-17 15:29:29]	link failed !, exit code: 1
   at unify_builder.Program.Main(String[] args_)
---
Error: L6218E: Undefined symbol delay_ms (referred from task.o).
Not enough information to list image symbols.
Finished: 1 information, 0 warning and 1 error messages.


[2025-07-17 15:30:36]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-17 15:30:37]	[done]
	build successfully !

[2025-07-17 16:08:41]	[info] incremental build: 3 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\key.c': source file has been changed.
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\main.c': source file has been changed.
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-17 16:08:42]	[done]
	build successfully !

[2025-07-17 17:00:26]	[info] incremental build: 3 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\key.c': source file has been changed.
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\main.c': source file has been changed.
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-17 17:00:27]	[done]
	build successfully !

[2025-07-17 17:17:37]	[info] incremental build: 2 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\key.c': source file has been changed.
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-17 17:17:38]	[done]
	build successfully !

[2025-07-17 17:38:59]	[info] incremental build: 2 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\key.c': source file has been changed.
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-17 17:39:00]	[done]
	build successfully !

[2025-07-17 17:46:14]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-17 17:46:15]	[done]
	build successfully !

[2025-07-17 17:51:42]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-17 17:51:43]	[done]
	build successfully !

[2025-07-17 17:53:30]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-17 17:53:31]	[done]
	build successfully !

[2025-07-18 15:02:27]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-18 15:02:28]	[done]
	build successfully !

[2025-07-18 15:07:17]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\Direction.c': source file has been changed.

[2025-07-18 15:07:18]	[done]
	build successfully !

[2025-07-18 15:13:25]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-18 15:13:26]	[done]
	build successfully !

[2025-07-18 15:17:31]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-18 15:17:31]	[done]
	build successfully !

[2025-07-23 08:35:33]	[info] incremental build: 4 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\Motor.c': source file has been changed.
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\key.c': source file has been changed.
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\main.c': source file has been changed.
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-23 08:35:35]	link failed !, exit code: 1
   at unify_builder.Program.Main(String[] args_)
---
Error: L6218E: Undefined symbol UpdateAndDisplayYaw (referred from task.o).
Not enough information to list image symbols.
Finished: 1 information, 0 warning and 1 error messages.


[2025-07-23 08:36:59]	link failed !, exit code: 1
   at unify_builder.Program.Main(String[] args_)
---
Error: L6218E: Undefined symbol UpdateAndDisplayYaw (referred from task.o).
Not enough information to list image symbols.
Finished: 1 information, 0 warning and 1 error messages.


[2025-07-23 08:37:46]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-23 08:37:46]	[done]
	build successfully !

[2025-07-23 14:47:32]	[info] incremental build: 3 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\Motor.c': source file has been changed.
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\fine_line.c': source file has been changed.
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-23 14:47:33]	link failed !, exit code: 1
   at unify_builder.Program.Main(String[] args_)
---
Error: L6218E: Undefined symbol UpdateAndDisplayYaw (referred from task.o).
Not enough information to list image symbols.
Finished: 1 information, 0 warning and 1 error messages.


[2025-07-23 14:48:12]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-23 14:48:12]	[done]
	build successfully !

[2025-07-23 15:04:14]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-23 15:04:15]	[done]
	build successfully !

[2025-07-23 15:14:05]	[info] incremental build: 2 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\main.c': dependence 'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Inc\task.h' has been changed.
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-23 15:14:06]	link failed !, exit code: 1
   at unify_builder.Program.Main(String[] args_)
---
Error: L6218E: Undefined symbol UpdateAndDisplayYaw (referred from task.o).
Not enough information to list image symbols.
Finished: 1 information, 0 warning and 1 error messages.


[2025-07-23 15:40:01]	[info] incremental build: 2 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\main.c': dependence 'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Inc\task.h' has been changed.
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-23 15:40:02]	link failed !, exit code: 1
   at unify_builder.Program.Main(String[] args_)
---
Error: L6218E: Undefined symbol UpdateAndDisplayYaw (referred from task.o).
Not enough information to list image symbols.
Finished: 1 information, 0 warning and 1 error messages.


[2025-07-23 15:50:03]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-23 15:50:04]	[done]
	build successfully !

[2025-07-23 15:51:59]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-23 15:51:59]	[done]
	build successfully !

[2025-07-23 16:02:50]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-23 16:02:51]	[done]
	build successfully !

[2025-07-23 16:06:08]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-23 16:06:08]	[done]
	build successfully !

[2025-07-23 16:08:33]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\fine_line.c': source file has been changed.

[2025-07-23 16:08:34]	[done]
	build successfully !

[2025-07-23 16:10:31]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\fine_line.c': source file has been changed.

[2025-07-23 16:10:31]	[done]
	build successfully !

[2025-07-23 16:14:02]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\fine_line.c': source file has been changed.

[2025-07-23 16:14:03]	[done]
	build successfully !

[2025-07-23 16:17:03]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-23 16:17:04]	[done]
	build successfully !

[2025-07-23 16:19:08]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'c:\Users\<USER>\Desktop\Ide To Keil\MDK-ARM\Task\Src\task.c': source file has been changed.

[2025-07-23 16:19:10]	[done]
	build successfully !

