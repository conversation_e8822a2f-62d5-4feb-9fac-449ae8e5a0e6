#include "main.h"
#include "stdio.h"
#include "fine_line.h"
#include "pid.h"
#include "MPU6050.h"
#include "Motor.h"
#include "OLED.h"
#include "Direction.h"
	unsigned char Do_count = 0;
	int res=1;
	float pitch;
	float roll;
	float yaw;
	int16_t gx,gy,gz,ax,ay,az;






void task_0()
{
	OLED_ShowSignedNum(2,10,0,3);
	SetSpeedR(0);
	SetSpeedL(0);
	HAL_Delay(1000);
}


void task_1()
{
	while(1)
	{
		if (Do_count ==0) {Do_count =1;}
		switch (Do_count)
		{
				//***?A?***//
			case 1:
			{
				MPU6050_DMP_Init();
				ControlYawMovement(30,0,35);
				OLED_ShowSignedNum(1,3,1,8) ;
				OLED_ShowSignedNum(2,3,g_fMPU6050YawMovePidOut1,3);
				OLED_ShowSignedNum(3,3,g_fMPU6050YawMovePidOut2,3);
				HAL_Delay(200);
				Do_count=2;
			}
			break;

			//***?B?***//
			case 2:
			{
				static uint8_t speed_set = 0;  // 速度设置标志
				static uint16_t detect_count = 0;  // 检测计数器

				// 只在第一次进入时设置速度和初始化MPU6050
				if(!speed_set) {
					//MPU6050_DMP_Init();
					ControlYawMovement(30,0,35);
					speed_set = 1;
					OLED_ShowSignedNum(1,3,2,8);
					OLED_ShowSignedNum(2,3,g_fMPU6050YawMovePidOut1,3);
				    OLED_ShowSignedNum(3,3,g_fMPU6050YawMovePidOut2,3);
				}

				// 每10次循环检测一次线，减少检测频率
				detect_count++;
				if(detect_count >= 10) {
					line_detect();
					get_LedFind_Scan();
					detect_count = 0;  // 重置计数器

					if(have_line==1){  //have line
						Do_count=3;
						speed_set = 0;  // 重置标志，为下次使用准备
					}
				}

				// 添加小延时，让系统稳定
				HAL_Delay(10);
			}
			break;

			case 3:
			{
				while(1){
					OLED_ShowSignedNum(1,3,3,8) ;
					SetSpeedR(0);
					SetSpeedL(0);;//??
					HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_RESET);
					     HAL_Delay(100);
					     HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_SET);
					     HAL_Delay(100);
				    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_RESET);
					     HAL_Delay(100);
					     HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_SET);
					     HAL_Delay(100);
			}
		}

	}


}
}
int findline_time = 0;
void task_2()
{   while(1)
	{if (Do_count ==0) {Do_count =1;}
	switch (Do_count)
		{
				//***?A?***//
			case 1:
			{
				OLED_Clear();  // 清屏，避免与按键系统显示冲突
				OLED_ShowString(1, 1, "TASK 2 RUNNING");
				OLED_ShowString(3, 1, "Status: 1") ;
				Do_count = 2;
				HAL_Delay(200);
 				MPU6050_DMP_Init();
				ControlYawMovement(30,0,35);

			}
			break;


			case 2:
			{
				static uint8_t case2_speed_set = 0;  // Case2速度设置标志
				static uint16_t case2_detect_count = 0;  // Case2检测计数器

				OLED_ShowString(3, 1, "Status: 2") ;

				// 只在第一次进入时设置速度和初始化MPU6050
				if(!case2_speed_set) {

				ControlYawMovement(23,0,35);
					case2_speed_set = 1;
				}

				// 每次循环都检测线，提高检测频率
				line_detect();
				get_LedFind_Scan();

				// 在OLED上显示have_line的值，用于调试
				OLED_ShowString(4, 1, "Line:");
				OLED_ShowNum(4, 6, have_line, 1);

				if(have_line==1)
				{		 // 声光
				       
							 SetSpeedR(0);
							 SetSpeedL(0);
							 
				    Do_count = 3;
					case2_speed_set = 0;  // 重置标志，为下次使用准备

				    
			   }


			}
			break;

			case 3:
			{	int NewYaw;
				OLED_ShowString(3, 1, "Status: 3") ;

				// 首先检测线的状态
				line_detect();
				get_LedFind_Scan();
				
				if(have_line == 1) {
					// 检测到线，执行巡线控制
					Find_Line_Begins();  // 执行巡线算法
					findline_time = 0;   // 重置失线计数器

					// 显示巡线状态
					OLED_ShowString(4, 1, "Following");

					// 显示线传感器状态（用于调试）
					OLED_ShowString(4, 10, "L:");
					OLED_ShowNum(4, 12, have_line, 1);

				} else {
					// 没有检测到线，可能偏离了线或到达线段末端
					findline_time++;

					// 显示失线状态和计数
					OLED_ShowString(4, 1, "Lost:");
					OLED_ShowNum(4, 6, findline_time, 2);

					// 尝试寻找线：继续前进
					if(findline_time <= 15) {
						// 前5次：继续前进，可能只是暂时失线
						MPU6050_DMP_Get_Data(&pitch, &roll, &yaw, &gx, &gy, &gz, &ax, &ay, &az);
						NewYaw = yaw;				
						ControlYawMovement(15, NewYaw, 20);
					}else {
						// 超过15次：认为到达线段末端，进入下一阶段
						findline_time = 0;
						Do_count = 4;
						OLED_ShowString(3, 1, "Status: 4") ;  // 显示进入Case 4

						// LED闪烁表示进入下一阶段
						HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_RESET);
						     HAL_Delay(100);
						     HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_SET);
						     HAL_Delay(100);
					    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_RESET);
						     HAL_Delay(100);
						     HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_SET);
						     HAL_Delay(100);
					}
				}

				// 添加延时，让系统稳定
				HAL_Delay(50);
			}
			break;

			case 4:
			{	int NewYaw;
				static uint8_t case4_speed_set = 0;  // Case4速度设置标志
				static uint16_t case4_detect_count = 0;  // Case4检测计数器

				OLED_ShowString(3, 1, "Status: 4") ;

				// 只在第一次进入时设置速度和初始化MPU6050
				if(!case4_speed_set) {
					MPU6050_DMP_Get_Data(&pitch, &roll, &yaw, &gx, &gy, &gz, &ax, &ay, &az);
					NewYaw = yaw;
					ControlYawMovement(60,NewYaw,65);
					case4_speed_set = 1;
				}

				// 每次循环都检测线，提高检测频率
				line_detect();
				get_LedFind_Scan();

				// 在OLED上显示have_line的值，用于调试
				OLED_ShowString(4, 1, "Line:");
				OLED_ShowNum(4, 6, have_line, 1);

				if(have_line==1){  //have line
				 Do_count=5;
				 case4_speed_set = 0;  // 重置标志，为下次使用准备
					HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_RESET);
					     HAL_Delay(100);
					     HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_SET);
					     HAL_Delay(100);
				    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_RESET);
					     HAL_Delay(100);
					     HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_SET);
					     HAL_Delay(100);
			   }

				// 添加小延时，让系统稳定
				HAL_Delay(50);  // 增加延时，让显示更稳定
			}
			break;
			case 5:
			{
				OLED_ShowString(3, 1, "Status: 5") ;
				Find_Line_Begins();
				if(have_line==0){  //have no line
					findline_time++;
				}
				else findline_time=0;
				if( findline_time>=3)
				{
					Do_count=6;
					OLED_ShowString(3, 1, "Status: 6") ;  // 显示进入Case 6
					HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_RESET);
					     HAL_Delay(100);
					     HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_SET);
					     HAL_Delay(100);
				    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_RESET);
					     HAL_Delay(100);
					     HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_SET);
					     HAL_Delay(100);

				}
			}
			break;

			case 6:
			{
				OLED_ShowString(3, 1, "Status: 6");
				while(1){
						SetSpeedR(0);
						SetSpeedL(0);
						HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_RESET);
					    HAL_Delay(100);
					    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_SET);
					    HAL_Delay(100);
				   		HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_RESET);
					    HAL_Delay(100);
					    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_SET);
					    HAL_Delay(100);
			}
		}
	}
	}
}
	int lose_time;
	void task_3()
	{

	while(1)
	{

		if ((Do_count==0)) {Do_count =1;}
		switch (Do_count)
		{
				//***?A?***//
			case 1:
			{
				OLED_ShowString(0,3,"1");
			    MPU6050_DMP_Init();
				ControlYawMovement(60,-70,65);
				line_detect();
				get_LedFind_Scan();
				if(have_line==1)
				{
					Do_count=2;
						HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_RESET);
					    HAL_Delay(100);
					    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_SET);
					    HAL_Delay(100);
				   		HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_RESET);
					    HAL_Delay(100);
					    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_SET);
					    HAL_Delay(100);
			   }
				}
				OLED_ShowString(0,3,"2") ;

			break;

			//***?C?***//
			  case 2:
			{      //P25 26 27 47 54 55 56 57
				line_detect();
				get_LedFind_Scan();
				Find_Line_Begins();
				if(have_line==0){  //have line
				 	Do_count=3;
				 	lose_time=0;
						HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_RESET);
					    HAL_Delay(100);
					    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_SET);
					    HAL_Delay(100);
				   		HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_RESET);
					    HAL_Delay(100);
					    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_SET);
					    HAL_Delay(100);
			   }

			}
			break;
			 //***?B?***//
			case 3:
			{
				OLED_ShowString(0,3,"3") ;
				Find_Line_Begins();
				if(have_line==0){  //have no line
					findline_time++;
				}
				else findline_time=0;
				if( findline_time>=3)
				{
					findline_time =0;
					Do_count=4;
 				MPU6050_DMP_Init();
				ControlYawMovement(60,70,65);
					OLED_ShowString(0,3,"4") ;
 				}

			}
			break;
			//***?D?***//
			case 4:
			{
				 MPU6050_DMP_Init();
				ControlYawMovement(60,70,65);
				line_detect();
				get_LedFind_Scan();


				if(have_line==1)
				{  //have line
				 Do_count=5;  lose_time=0;
						HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_RESET);
					    HAL_Delay(100);
					    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_SET);
					    HAL_Delay(100);
				   		HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_RESET);
					    HAL_Delay(100);
					    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_SET);
					    HAL_Delay(100);
			   }
			}
			break;
			//***?A?***//
			case 5:
			{
				OLED_ShowString(0,3,"5") ;
				Find_Line_Begins();
				if(have_line==0)
				{  //have no line
					findline_time++;
				}
				else findline_time=0;
				if( findline_time>=3)
				{
					Do_count=6;

				}

			}
			break;
			case 6:
			{
				OLED_ShowString(0,3,"6") ;
				while(1)
				{
				SetSpeedR(0);
				SetSpeedL(0);
				HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_RESET);
				HAL_Delay(100);
				HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_SET);
				HAL_Delay(100);
				HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_RESET);
				HAL_Delay(100);
				HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_SET);
				HAL_Delay(100);
			}
		}
	}
	}
}




/**
  * @brief  任务4：循环执行三次task_3
  * @param  无
  * @retval 无
  */
void task_4(void)
{
	static uint8_t task4_cycle_count = 0;  // 循环计数器
	static uint8_t task4_initialized = 0;  // 初始化标志

	// 初始化显示
	if(!task4_initialized) {
		OLED_Clear();
		OLED_ShowString(1, 1, "TASK 4 RUNNING");
		OLED_ShowString(2, 1, "Cycle 1/3");
		task4_cycle_count = 0;
		task4_initialized = 1;
		HAL_Delay(500);  // 显示初始信息
	}

	// 显示当前循环次数
	OLED_ShowString(3, 1, "Status: Active");
	OLED_ShowString(2, 1, "Cycle ");
	OLED_ShowNum(2, 7, task4_cycle_count + 1, 1);
	OLED_ShowString(2, 8, "/3");

	// 执行task_3的逻辑（简化版本，避免无限循环）
	OLED_ShowString(4, 1, "Running Task3");

	// 模拟task_3的核心功能

	// 简单的运动控制示例
	MPU6050_DMP_Init();
	ControlYawMovement(30, 0, 35);
	HAL_Delay(2000);  // 运行2秒

	// 停止运动
	SetSpeedR(0);
	SetSpeedL(0);

	// LED闪烁表示一次循环完成
	for(int i = 0; i < 3; i++) {
		HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_RESET);
		HAL_Delay(200);
		HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_SET);
		HAL_Delay(200);
	}

	// 增加循环计数
	task4_cycle_count++;

	// 检查是否完成三次循环
	if(task4_cycle_count >= 3) {
		// 三次循环完成，显示完成信息
		OLED_Clear();
		OLED_ShowString(1, 1, "TASK 4 COMPLETE");
		OLED_ShowString(2, 1, "3 Cycles Done");
		OLED_ShowString(3, 1, "System Stopped");

		// 最终LED闪烁
		for(int i = 0; i < 6; i++) {
			HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_RESET);
			HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_RESET);
			HAL_Delay(300);
			HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_SET);
			HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_SET);
			HAL_Delay(300);
		}

		// 重置标志，为下次执行准备
		task4_cycle_count = 0;
		task4_initialized = 0;

		// 任务完成，进入无限循环
		while(1) {
			HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_6);
			HAL_Delay(1000);
		}
	} else {
		// 准备下一次循环
		HAL_Delay(1000);  // 循环间隔
	}
}