#ifndef __KEY_H
#define __KEY_H

#include "main.h"

// 定义按键引脚
#define KEY_PORT        GPIOB
#define KEY_PIN         GPIO_PIN_1
#define KEY_CLK_ENABLE  __HAL_RCC_GPIOB_CLK_ENABLE()

// 定义任务数量
#define TASK_COUNT      5

// 按键状态枚举
typedef enum {
    KEY_UP = 0,     // 按键抬起
    KEY_DOWN,       // 按键按下
    KEY_LONG_PRESS  // 按键长按
} KeyState;

// 函数声明
void KEY_Init(void);                  // 按键初始化
KeyState KEY_Scan(uint8_t mode);      // 按键扫描
uint8_t KEY_GetTaskIndex(void);       // 获取当前任务索引
void KEY_SwitchTask(void);            // 切换到下一个任务
void KEY_UpdateOLED(void);            // 更新OLED显示
uint8_t KEY_IsSelectionMode(void);    // 检查是否在选择模式
void KEY_UpdateSelectionTimer(void);  // 更新选择模式计时器
uint8_t KEY_IsTaskExecuted(void);     // 检查任务是否已执行
void KEY_SetTaskExecuted(void);       // 设置任务已执行标志

#endif /* __KEY_H */