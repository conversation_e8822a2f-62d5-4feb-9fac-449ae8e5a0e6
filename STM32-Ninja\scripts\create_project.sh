#!/bin/bash
# STM32 Ninja - 创建项目脚本

# 加载通用函数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh" || exit 1

# 参数检查
if [ $# -lt 2 ]; then
    error "用法: $0 <项目名> <MCU型号>"
    exit 1
fi

PROJECT_NAME="$1"
MCU_TYPE="$2"

# MCU配置映射
get_mcu_config() {
    case "$1" in
        STM32F103C8T6)
            echo "cortex-m3 STM32F103x8"
            ;;
        STM32F103RCT6)
            echo "cortex-m3 STM32F103xC"
            ;;
        STM32F407VGT6)
            echo "cortex-m4 STM32F407xx"
            ;;
        *)
            echo "cortex-m3 STM32F103x8"
            ;;
    esac
}

# 获取MCU配置
read cpu_type mcu_define <<< $(get_mcu_config "$MCU_TYPE")

info "创建项目: $PROJECT_NAME"
info "MCU型号: $MCU_TYPE"

# 创建项目目录结构
mkdir -p "$PROJECT_NAME"/{src,inc,lib,cmake,debug}

# 创建CMakeLists.txt
cat > "$PROJECT_NAME/CMakeLists.txt" << EOF
cmake_minimum_required(VERSION 3.16)

# 项目配置
set(CMAKE_PROJECT_NAME $PROJECT_NAME)
project(\${CMAKE_PROJECT_NAME} C CXX ASM)

# 设置C/C++标准
set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 17)

# MCU定义
add_definitions(-D$mcu_define)

# 包含目录
include_directories(
    inc
    lib/CMSIS/Include
    lib/CMSIS/Device/ST/STM32F1xx/Include
)

# 源文件
file(GLOB_RECURSE SOURCES 
    "src/*.c"
    "src/*.cpp"
    "lib/*.c"
    "startup/*.s"
)

# 创建可执行文件
add_executable(\${CMAKE_PROJECT_NAME}.elf \${SOURCES})

# 链接脚本
set(LINKER_SCRIPT \${CMAKE_SOURCE_DIR}/STM32F103C8Tx_FLASH.ld)
target_link_options(\${CMAKE_PROJECT_NAME}.elf PRIVATE
    -T\${LINKER_SCRIPT}
    -Wl,-Map=\${CMAKE_PROJECT_NAME}.map,--cref
)

# 生成hex和bin文件
add_custom_command(TARGET \${CMAKE_PROJECT_NAME}.elf POST_BUILD
    COMMAND \${CMAKE_OBJCOPY} -O ihex \${CMAKE_PROJECT_NAME}.elf \${CMAKE_PROJECT_NAME}.hex
    COMMAND \${CMAKE_OBJCOPY} -O binary \${CMAKE_PROJECT_NAME}.elf \${CMAKE_PROJECT_NAME}.bin
    COMMENT "生成HEX和BIN文件"
)
EOF

# 创建工具链文件
cat > "$PROJECT_NAME/cmake/toolchain.cmake" << EOF
set(CMAKE_SYSTEM_NAME Generic)
set(CMAKE_SYSTEM_PROCESSOR arm)

# 编译器设置
set(CMAKE_C_COMPILER arm-none-eabi-gcc)
set(CMAKE_CXX_COMPILER arm-none-eabi-g++)
set(CMAKE_ASM_COMPILER arm-none-eabi-gcc)
set(CMAKE_OBJCOPY arm-none-eabi-objcopy)
set(CMAKE_SIZE arm-none-eabi-size)

# MCU参数
set(MCU_FLAGS "-mcpu=$cpu_type -mthumb")

# 编译选项
set(CMAKE_C_FLAGS "\${MCU_FLAGS} -Wall -fdata-sections -ffunction-sections")
set(CMAKE_CXX_FLAGS "\${CMAKE_C_FLAGS} -fno-rtti -fno-exceptions")
set(CMAKE_ASM_FLAGS "\${MCU_FLAGS} -x assembler-with-cpp")

# 链接选项
set(CMAKE_EXE_LINKER_FLAGS "\${MCU_FLAGS} -Wl,--gc-sections -Wl,--print-memory-usage -specs=nano.specs -specs=nosys.specs")

# 构建类型
set(CMAKE_C_FLAGS_DEBUG "-O0 -g3 -DDEBUG")
set(CMAKE_C_FLAGS_RELEASE "-O2 -DNDEBUG")
set(CMAKE_CXX_FLAGS_DEBUG "\${CMAKE_C_FLAGS_DEBUG}")
set(CMAKE_CXX_FLAGS_RELEASE "\${CMAKE_C_FLAGS_RELEASE}")
EOF

# 创建主程序文件
cat > "$PROJECT_NAME/src/main.c" << 'EOF'
#include <stdint.h>

// LED引脚定义 (PC13 - Blue Pill板载LED)
#define LED_PIN 13

// 简单延时函数
void delay(uint32_t count) {
    for(uint32_t i = 0; i < count; i++) {
        __asm__("nop");
    }
}

// GPIO初始化
void gpio_init(void) {
    // 使能GPIOC时钟
    *(volatile uint32_t*)0x40021018 |= (1 << 4);
    
    // 配置PC13为推挽输出
    *(volatile uint32_t*)0x40011004 &= ~(0xF << 20);
    *(volatile uint32_t*)0x40011004 |= (0x1 << 20);
}

int main(void) {
    // 初始化GPIO
    gpio_init();
    
    // 主循环 - LED闪烁
    while(1) {
        // LED亮
        *(volatile uint32_t*)0x40011010 &= ~(1 << LED_PIN);
        delay(1000000);
        
        // LED灭
        *(volatile uint32_t*)0x40011010 |= (1 << LED_PIN);
        delay(1000000);
    }
    
    return 0;
}
EOF

# 创建启动文件（简化版）
cat > "$PROJECT_NAME/startup/startup.s" << 'EOF'
.syntax unified
.cpu cortex-m3
.thumb

.global g_pfnVectors
.global Default_Handler

.section .isr_vector,"a",%progbits
.type g_pfnVectors, %object

g_pfnVectors:
    .word _estack
    .word Reset_Handler
    .word NMI_Handler
    .word HardFault_Handler
    /* 更多中断向量... */

.section .text.Reset_Handler
.weak Reset_Handler
.type Reset_Handler, %function

Reset_Handler:
    /* 设置栈指针 */
    ldr sp, =_estack
    
    /* 复制数据段 */
    movs r1, #0
    b LoopCopyDataInit

CopyDataInit:
    ldr r3, =_sidata
    ldr r3, [r3, r1]
    str r3, [r0, r1]
    adds r1, r1, #4

LoopCopyDataInit:
    ldr r0, =_sdata
    ldr r3, =_edata
    adds r2, r0, r1
    cmp r2, r3
    bcc CopyDataInit
    
    /* 清零BSS段 */
    ldr r2, =_sbss
    ldr r4, =_ebss
    movs r3, #0
    b LoopFillZerobss

FillZerobss:
    str r3, [r2]
    adds r2, r2, #4

LoopFillZerobss:
    cmp r2, r4
    bcc FillZerobss
    
    /* 调用main函数 */
    bl main
    bx lr

.size Reset_Handler, .-Reset_Handler

/* 默认中断处理 */
.section .text.Default_Handler,"ax",%progbits
Default_Handler:
Infinite_Loop:
    b Infinite_Loop

.size Default_Handler, .-Default_Handler

/* 弱定义的中断处理函数 */
.weak NMI_Handler
.thumb_set NMI_Handler,Default_Handler

.weak HardFault_Handler
.thumb_set HardFault_Handler,Default_Handler
EOF

# 创建链接脚本
cat > "$PROJECT_NAME/STM32F103C8Tx_FLASH.ld" << 'EOF'
ENTRY(Reset_Handler)

_estack = 0x20005000;    /* RAM结束地址 (20KB) */

MEMORY
{
    FLASH (rx)  : ORIGIN = 0x08000000, LENGTH = 64K
    RAM (xrw)   : ORIGIN = 0x20000000, LENGTH = 20K
}

SECTIONS
{
    .isr_vector :
    {
        . = ALIGN(4);
        KEEP(*(.isr_vector))
        . = ALIGN(4);
    } >FLASH

    .text :
    {
        . = ALIGN(4);
        *(.text)
        *(.text*)
        *(.rodata)
        *(.rodata*)
        . = ALIGN(4);
        _etext = .;
    } >FLASH

    _sidata = LOADADDR(.data);

    .data :
    {
        . = ALIGN(4);
        _sdata = .;
        *(.data)
        *(.data*)
        . = ALIGN(4);
        _edata = .;
    } >RAM AT> FLASH

    .bss :
    {
        . = ALIGN(4);
        _sbss = .;
        *(.bss)
        *(.bss*)
        *(COMMON)
        . = ALIGN(4);
        _ebss = .;
    } >RAM
}
EOF

# 创建README
cat > "$PROJECT_NAME/README.md" << EOF
# $PROJECT_NAME

STM32项目，使用STM32 Ninja工具创建。

## MCU信息
- 型号: $MCU_TYPE
- 内核: $cpu_type

## 快速开始

1. 编译项目:
   \`\`\`bash
   ../ninja.sh build
   \`\`\`

2. 烧录程序:
   \`\`\`bash
   ../ninja.sh flash
   \`\`\`

3. 编译并烧录:
   \`\`\`bash
   ../ninja.sh all
   \`\`\`

## 项目结构
- \`src/\` - 源代码文件
- \`inc/\` - 头文件
- \`lib/\` - 库文件
- \`cmake/\` - CMake配置
- \`debug/\` - 调试配置
- \`build/\` - 编译输出（自动生成）

## 示例代码
项目包含一个简单的LED闪烁示例，LED连接在PC13引脚（Blue Pill板载LED）。
EOF

success "项目创建成功！"
echo ""
echo "项目位置: $(pwd)/$PROJECT_NAME"
echo ""
echo "下一步："
echo "  1. cd $PROJECT_NAME"
echo "  2. 编辑 src/main.c 添加您的代码"
echo "  3. 使用 '../ninja.sh build' 编译"
echo "  4. 使用 '../ninja.sh flash' 烧录"

exit 0