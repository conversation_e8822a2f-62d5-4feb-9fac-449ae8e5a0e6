#ifndef __MOTOR_H
#define __MOTOR_H

#include "main.h"

// 宏定义：PWM通道（可根据实际修改）
#define MOTOR_RIGHT_PWM_CHANNEL TIM_CHANNEL_3
#define MOTOR_LEFT_PWM_CHANNEL  TIM_CHANNEL_4

// 宏定义：右电机方向控制引脚
#define MOTOR_R_IN1_PORT GPIOA
#define MOTOR_R_IN1_PIN  PA4Motor_Pin
#define MOTOR_R_IN2_PORT GPIOA
#define MOTOR_R_IN2_PIN  PA5Motor_Pin

// 宏定义：左电机方向控制引脚
#define MOTOR_L_IN1_PORT GPIOA
#define MOTOR_L_IN1_PIN  PA6Motor_Pin
#define MOTOR_L_IN2_PORT GPIOA
#define MOTOR_L_IN2_PIN  PA7Motor_Pin

// 外部变量：使用的定时器
extern TIM_HandleTypeDef htim2;

// 函数声明
void SetSpeedR(int Speed);
void SetSpeedL(int Speed);

#endif
