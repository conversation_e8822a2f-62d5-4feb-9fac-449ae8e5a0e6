*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'C:\Keil_v5\ARM\ARMCC\Bin'
Build target 'Ide To Keil'
Note: source file '.\Task\Src\main.c' - object file renamed from 'Ide To Keil\main.o' to 'Ide To Keil\main_1.o'.compiling key.c...
.\Task\Inc\key.h(28): warning:  #1-D: last line of file ends without a newline
  #endif /* __KEY_H */
Task\Src\key.c(123): warning:  #1-D: last line of file ends without a newline
  }
Task\Src\key.c: 2 warnings, 0 errors
compiling gpio.c...
../Core/Src/gpio.c(101): warning:  #1-D: last line of file ends without a newline
  /* USER CODE END 2 */
../Core/Src/gpio.c: 1 warning, 0 errors
compiling main.c...
.\Task\Inc\key.h(28): warning:  #1-D: last line of file ends without a newline
  #endif /* __KEY_H */
Task\Src\main.c(244): warning:  #1-D: last line of file ends without a newline
  #endif /* USE_FULL_ASSERT */
Task\Src\main.c: 2 warnings, 0 errors
linking...
Program Size: Code=26474 RO-data=5222 RW-data=192 ZI-data=1960  
FromELF: creating hex file...
"Ide To Keil\Ide To Keil.axf" - 0 Error(s), 5 Warning(s).
Build Time Elapsed:  00:00:02
