#!/bin/bash
# STM32 Ninja - 默认配置设置

# 加载通用函数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh" || exit 1

# 配置文件路径
CONFIG_FILE="${NINJA_ROOT:-$(dirname "$SCRIPT_DIR")}/config/ninja.conf"

# 主程序
main() {
    info "默认配置设置"
    echo ""
    
    # 加载现有配置
    if [ -f "$CONFIG_FILE" ]; then
        source "$CONFIG_FILE"
    fi
    
    # MCU型号
    echo "1. 默认MCU型号"
    echo "   当前设置: ${DEFAULT_MCU:-STM32F103C8T6}"
    echo "   常见型号:"
    echo "     1) STM32F103C8T6 (Blue Pill)"
    echo "     2) STM32F103RCT6"
    echo "     3) STM32F407VGT6"
    echo "     4) STM32F429ZIT6"
    echo "     5) 其他"
    echo -n "   选择 [1-5，回车保持当前]: "
    read choice
    
    case "$choice" in
        1) DEFAULT_MCU="STM32F103C8T6" ;;
        2) DEFAULT_MCU="STM32F103RCT6" ;;
        3) DEFAULT_MCU="STM32F407VGT6" ;;
        4) DEFAULT_MCU="STM32F429ZIT6" ;;
        5) 
            echo -n "   输入MCU型号: "
            read DEFAULT_MCU
            ;;
    esac
    
    # 编译类型
    echo ""
    echo "2. 默认编译类型"
    echo "   当前设置: ${DEFAULT_BUILD_TYPE:-Debug}"
    echo "     1) Debug (调试模式)"
    echo "     2) Release (发布模式)"
    echo -n "   选择 [1-2，回车保持当前]: "
    read choice
    
    case "$choice" in
        1) DEFAULT_BUILD_TYPE="Debug" ;;
        2) DEFAULT_BUILD_TYPE="Release" ;;
    esac
    
    # 烧录器
    echo ""
    echo "3. 默认烧录器"
    echo "   当前设置: ${DEFAULT_PROGRAMMER:-stlink}"
    echo "     1) ST-Link"
    echo "     2) CMSIS-DAP"
    echo "     3) J-Link"
    echo -n "   选择 [1-3，回车保持当前]: "
    read choice
    
    case "$choice" in
        1) DEFAULT_PROGRAMMER="stlink" ;;
        2) DEFAULT_PROGRAMMER="cmsis-dap" ;;
        3) DEFAULT_PROGRAMMER="jlink" ;;
    esac
    
    # API设置
    echo ""
    echo "4. API服务设置"
    echo -n "   启用API服务? [Y/n，当前: ${API_ENABLED:-true}]: "
    read choice
    if [ "$choice" = "n" ] || [ "$choice" = "N" ]; then
        API_ENABLED=false
    else
        API_ENABLED=true
    fi
    
    echo -n "   API端口 [当前: ${API_PORT:-8080}]: "
    read new_port
    if [ -n "$new_port" ]; then
        API_PORT="$new_port"
    fi
    
    # 保存配置
    echo ""
    step "保存配置..."
    
    # 确保其他配置值存在
    CUBECLT_PATH="${CUBECLT_PATH:-/d/app/cubeclt/STM32CubeCLT_1.18.0}"
    MSYS2_PATH="${MSYS2_PATH:-/d/app/msys2}"
    
    mkdir -p "$(dirname "$CONFIG_FILE")"
    cat > "$CONFIG_FILE" << EOF
# STM32 Ninja 配置文件
# 自动生成于 $(date)

# 工具路径配置
CUBECLT_PATH="$CUBECLT_PATH"
MSYS2_PATH="$MSYS2_PATH"

# 默认设置
DEFAULT_MCU="$DEFAULT_MCU"
DEFAULT_BUILD_TYPE="$DEFAULT_BUILD_TYPE"
DEFAULT_PROGRAMMER="$DEFAULT_PROGRAMMER"

# AI接口设置
API_ENABLED=$API_ENABLED
API_PORT=$API_PORT
EOF
    
    success "配置已保存"
    
    echo ""
    echo "当前配置："
    echo "  MCU型号: $DEFAULT_MCU"
    echo "  编译类型: $DEFAULT_BUILD_TYPE"
    echo "  烧录器: $DEFAULT_PROGRAMMER"
    echo "  API服务: $API_ENABLED (端口: $API_PORT)"
}

# 执行主程序
main