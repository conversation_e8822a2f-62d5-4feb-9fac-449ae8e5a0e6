# STM32 Ninja Helper Script for Windows
param(
    [Parameter(Position=0)]
    [string]$Command = "help"
)

function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Show-ProjectStatus {
    Write-Info "STM32 Ninja Project Status"
    Write-Host "=========================" -ForegroundColor Cyan
    
    if (Test-Path "build.ninja") {
        Write-Info "Project initialized: YES"
        
        if (Test-Path "config/ninja.conf") {
            $config = Get-Content "config/ninja.conf"
            foreach ($line in $config) {
                if ($line -match "PROJECT_TYPE=(.+)") {
                    Write-Info "Project type: $($matches[1])"
                }
                if ($line -match "INIT_DATE=(.+)") {
                    Write-Info "Init date: $($matches[1])"
                }
            }
        }
        
        if (Test-Path "build/mcu_info.conf") {
            $mcuInfo = Get-Content "build/mcu_info.conf" -Raw
            Write-Info "MCU: $mcuInfo"
        }
        
        # Check build files
        if (Test-Path "build/Ide To Keil.elf") {
            $elfFile = Get-Item "build/Ide To Keil.elf"
            Write-Info "ELF file: $($elfFile.Length) bytes"
        }
        
        if (Test-Path "build/Ide To Keil.hex") {
            $hexFile = Get-Item "build/Ide To Keil.hex"
            Write-Info "HEX file: $($hexFile.Length) bytes"
        }
        
    } else {
        Write-Error "Project not initialized"
    }
}

function Build-Project {
    Write-Info "Building project with Ninja..."
    
    $startTime = Get-Date
    ninja
    $endTime = Get-Date
    $duration = $endTime - $startTime
    
    if ($LASTEXITCODE -eq 0) {
        Write-Info "Build successful! Time: $($duration.TotalSeconds.ToString('F2'))s"
    } else {
        Write-Error "Build failed!"
    }
}

function Clean-Project {
    Write-Info "Cleaning project..."
    
    if (Test-Path "build") {
        Remove-Item -Recurse -Force "build" -ErrorAction SilentlyContinue
        Write-Info "Cleaned build directory"
    }
    
    Write-Info "Clean completed"
}

function List-Files {
    Write-Info "Project Files:"
    Write-Host "==============" -ForegroundColor Cyan
    
    if (Test-Path "build.ninja") {
        $buildFile = Get-Content "build.ninja"
        $sourceFiles = @()
        
        foreach ($line in $buildFile) {
            if ($line -match "build .+: cc (.+\.c)") {
                $sourceFiles += $matches[1]
            }
        }
        
        Write-Host "Source files ($($sourceFiles.Count)):" -ForegroundColor Yellow
        foreach ($file in $sourceFiles | Sort-Object) {
            if (Test-Path $file) {
                Write-Host "  [OK] $file" -ForegroundColor Green
            } else {
                Write-Host "  [MISSING] $file" -ForegroundColor Red
            }
        }
    }
}

function Show-Help {
    Write-Host "STM32 Ninja Helper Script" -ForegroundColor Cyan
    Write-Host "=========================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Commands:" -ForegroundColor Yellow
    Write-Host "  build   - Build the project using ninja" -ForegroundColor White
    Write-Host "  clean   - Clean build files" -ForegroundColor White
    Write-Host "  status  - Show project status" -ForegroundColor White
    Write-Host "  list    - List project files" -ForegroundColor White
    Write-Host "  help    - Show this help" -ForegroundColor White
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\ninja-helper.ps1 build" -ForegroundColor White
    Write-Host "  .\ninja-helper.ps1 clean" -ForegroundColor White
    Write-Host "  .\ninja-helper.ps1 status" -ForegroundColor White
}

# Main logic
switch ($Command.ToLower()) {
    "build" { Build-Project }
    "clean" { Clean-Project }
    "status" { Show-ProjectStatus }
    "list" { List-Files }
    "help" { Show-Help }
    default { 
        Write-Error "Unknown command: $Command"
        Show-Help 
    }
}
